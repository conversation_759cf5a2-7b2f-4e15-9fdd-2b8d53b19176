<script setup lang="ts">
import { ref, computed, onMounted, toRaw, nextTick, watch } from 'vue';
import { baseFastapiURL } from '../main';
import { Download } from "lucide-vue-next";
import axios from 'axios'
import DynamicFilter from './common/DynamicFilter.vue'
import ProductivityChartsDashboard from './ProductivityChartsDashboard.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { updateScenarioStatus } from '../services/api.js'
import { useRouter } from 'vue-router'
import AlertPage from "./common/AlertPage.vue"


const router = useRouter()
const alertMessage = ref(`Your setup is now complete.
The evaluation will run in the background.
Please return after the evaluation period to view the results.`)
const headerMessage = ref("All Steps Completed")
const showAlert = ref(false)

const handleOk = () => {
  completeOptimization()
  saveOptimiserProgress('next')
  showAlert.value = false
}


const handleCancel = () => {
  showAlert.value = false
}

// Track which cell is being edited: { [rowKey_colKey]: true }
const editingCells = ref({})

function getCellKey(row, colKey) {
  return row.LOC_CD + '-' + row.SUB_CLSS_NM + '-' + colKey
}

function startEdit(row, colKey) {
  editingCells.value[getCellKey(row, colKey)] = true
}

function saveEdit(row, colKey) {
  editingCells.value[getCellKey(row, colKey)] = false
  recordChange(row, colKey, row[colKey])
}

function isEditing(row, colKey) {
  return !!editingCells.value[getCellKey(row, colKey)]
}

const stepsStore = useStepsStore()
const storedData = stepsStore.getScenarioData;
let optimizationData = ref([]);
let upliftData = ref([]);
let allStores = ref([]);
let dataCount = ref(10);

const isLoading = ref(false)
const showProductivityModal = ref(false)

watch(showProductivityModal, async (val) => {
  if (val) {
    await nextTick()
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 0)
  }
})
const fetchOptimizationData = async (current_page = 1, limit = 10) => {
  isLoading.value = true
  try {
    const response = await axios.post('scenario/getOptimizationSummary/', {
      concept: sessionStorage.getItem('concept'),
      territory: sessionStorage.getItem('territory_name'),
      scenario_id: sessionStorage.getItem('scenario_id'),
      group: selectedGroups.value,
      department: selectedDepartments.value,
      class_field: selectedClasses.value,
      sub_class: selectedSubClasses.value,
      loc_cd: selectedStores.value ? selectedStores.value : [],
      recommendation: selectedRecommendation.value,
      page: current_page,
      limit: limit
    })
    optimizationData.value = response.data.data;
    allStores.value = response.data.locations;
    dataCount.value = response.data.count
    showFiltered.value = false;
    selectedStores.value = optimizationData?.value[0]?.LOC_CD
    if(response.data.data.length === 0) {
      await runOptimizer('optimization')
    }
  } catch (err) {
    console.error('Error fetching optimization Data:', err)
  } finally {
    isLoading.value = false
  }
}
async function getUpliftData() {
  try {
    const response = await axios.post('/scenario/getUpliftData/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
      concept: sessionStorage.getItem('concept'),
    })
    upliftData.value = response.data.data
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}

const runOptimizer = async (module) => {
  isLoading.value = true
  try {
    const response = await fetch(`${baseFastapiURL}/run/${module}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        SCENARIO_ID: String(sessionStorage.getItem('scenario_id')),
      })
    });

    if (!response.ok) {
      throw new Error(`API error ${response.status}: ${response.statusText}`);
    }
    await new Promise(resolve => setTimeout(resolve, 2000));
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    const json = await response.json();
    return json;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e; // rethrow so caller knows it failed
  } finally {
    isLoading.value = false
  }
};
const reRunOptimizer = async (module) => {
  isLoading.value = true
  try {
    const response = await fetch(`${baseFastapiURL}/run/${module}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        SCENARIO_ID: String(sessionStorage.getItem('scenario_id')),
        OVERRIDE_DICT: toRaw(changedRecords.value)
      })
    });

    if (!response.ok) {
      throw new Error(`API error ${response.status}: ${response.statusText}`);
    }
    await new Promise(resolve => setTimeout(resolve, 2000));
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    changedRecords = ref({})
    const json = await response.json();
    return json;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e; // rethrow so caller knows it failed
  } finally {
    isLoading.value = false
  }
};

onMounted(() => {
  ; (async () => {
    // await getGdcsData()
    if(sessionStorage.getItem('runoptimiser') === 'true') {
      await runOptimizer('optimization')
      sessionStorage.setItem('runoptimiser', 'false')
    }
    else{
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    }
    await getUpliftData()
  })()
})

const calcMetricChangePercent = (row: any) => {
  const val = parseFloat(row[metric.baseKeyColoumnData])
  const optMetric = parseFloat(row.new_metric)

  if (!isNaN(val) && val !== 0 && !isNaN(optMetric)) {
    return Math.round(((optMetric - val) / val) * 100)
  }

  return '-'
}


// Capitalize and color-code recommendation
const capitalizeRecommendation = (val: any) => {
  if (!val) return ''
  return String(val).charAt(0).toUpperCase() + String(val).slice(1).toLowerCase()
}

const recommendationClass = (val: any) => {
  const v = String(val).toLowerCase()
  if (v === 'increase') return 'bg-green-100 text-green-700 font-semibold px-2 py-1 rounded'
  if (v === 'decrease') return 'bg-red-100 text-red-700 font-semibold px-2 py-1 rounded'
  if (v === 'keep') return 'bg-gray-100 text-gray-700 font-semibold px-2 py-1 rounded'
  return ''
}


const performanceMetric = sessionStorage.getItem('performance_metric')
// Define mapping for labels & keys
const metricConfig = {
  GMV: {
    baseKey: 'GMV_sum_reference_month',
    baseKeyColoumnData: 'GMV_sum_reference_month',
    baseLabel: 'GMV',
    optKey: 'GMV_sum_optimized',
    optLabel: 'Optimized GMV',
    changeLabel: 'GMV Change %',
  },
  REVENUE: {
    baseKey: 'Revenue_sum_reference_month',
    baseKeyColoumnData: 'NET_SLS_AMT_sum_reference_month',
    baseLabel: 'Revenue',
    optKey: 'Revenue_sum_optimized',
    optLabel: 'Optimized Revenue',
    changeLabel: 'Revenue Change %',
  }
}
const metric = metricConfig[performanceMetric] || metricConfig.GMV
let metricShortNm = performanceMetric === 'REVENUE' ? 'rev' : 'gmv'
const tableColumns = [
  { key: 'LOC_CD', label: 'Store' },
  { key: 'GRP_NM', label: 'Group' },
  { key: 'DPT_NM', label: 'Department' },
  { key: 'CLSS_NM', label: 'Class' },
  { key: 'SUB_CLSS_NM', label: 'Subclass' },
  { key: 'MIN_LM', label: 'Min LM' },
  { key: 'max_sat_lm', label: 'Max LM' },
  { key: 'current_lm', label: 'Current LM' },
  { key: 'optimized_lm', label: 'Optimized LM' },
  { key: 'lm_delta', label: 'Space Change Absolute' },
  { key: 'space_change_precent', label: 'Space Change %' },
  { key: `${metric.baseKeyColoumnData}`, label: metric.baseLabel, numeric: true },
  { key: 'new_metric', label: metric.optLabel, numeric: true },
  { key: `${metricShortNm}_change_percent`, label: metric.changeLabel },
  { key: 'Final_Action', label: 'Recommendation' },
  { key: 'optimized_no_of_options', label: 'Optimized Options' },
  { key: 'DEPTH', label: 'Depth' },
  { key: 'optimized_qty', label: 'Optimized Quantity' }
  
]

const currentPage = ref(1)
const rowsPerPage = 10
let changedRecords = ref({})

const totalPages = computed(() =>
  Math.ceil(dataCount.value / rowsPerPage)
)

const filteredData = ref([])
const showFiltered = ref(false)


async function applyFilters() {
  showFiltered.value = true
  currentPage.value = 1

  await fetchOptimizationData(currentPage.value, rowsPerPage)
}

const paginatedData = computed(() => {
  return showFiltered.value ? filteredData.value : optimizationData.value
})

const capitalizeWords = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
}

const formatValue = (val: any, isNumeric = false, colKey = '') => {
  if (val === null || val === '') return '-'
  const textFieldsToCapitalize = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM']

  if (typeof val === 'string' && textFieldsToCapitalize.includes(colKey)) {
    return capitalizeWords(val)
  }
  if (typeof val === 'number') {
    const noDecimalFields = ['Revenue_sum_reference_month', 'Revenue_sum_optimized', 'GMV_sum_reference_month','GMV_sum_optimized',
               'new_metric', 'optimized_no_of_options', 'DEPTH', 'optimized_qty', 'lm_delta', 'space_change_precent'];
    if (noDecimalFields.includes(colKey)) {
      return Math.round(val).toLocaleString('en-US', {
        maximumFractionDigits: 0
      })
    }
    if (isNumeric) {
      return Math.round(val).toLocaleString('en-US', { maximumFractionDigits: 0 })
    }
    return val.toFixed(2)
  }
  return val
}

const recordChange = (row, key, newValue) => {
  // If editing space_change_precent, convert to absolute and update lm_delta
  if (key === 'space_change_precent') {
    // newValue is percent (e.g. 10 for 10%)
    const percent = parseFloat(newValue)
    const currentLm = parseFloat(row.current_lm)
    if (!isNaN(percent) && !isNaN(currentLm)) {
      // Calculate new absolute value
      // const absDelta = Math.round((currentLm * percent / 100 + currentLm) * 100) / 100
      const absDelta = Math.round(currentLm * percent / 100 + currentLm)
      row.lm_delta = absDelta
      // Also update changedRecords for lm_delta
      if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {}
      changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = absDelta
      return
    }
  }
  if (key === 'lm_delta') {
  const delta = parseFloat(newValue);
  const currentLm = parseFloat(row.current_lm);

  if (!isNaN(delta) && !isNaN(currentLm) && currentLm !== 0) {
    // const percent = Math.round((delta / currentLm) * 10000) / 100; // 2 decimal places
    const percent = Math.round((delta / currentLm) * 100);
    row.space_change_precent = percent;

    if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {};
    changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = percent;
    return;
  }
}

  // For lm_delta or other direct edits
  if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {}
  changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = newValue
}
const selectedStores = ref([])
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubClasses = ref([])
const selectedRecommendation = ref([])
// Reset methods
const resetOnStoreChange = () => {
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnGroupChange = () => {
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnDepartmentChange = () => {
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnClassChange = () => {
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}
const clearAllFilters = () => {
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const uniqueStores = computed(() => {
  const stores = allStores.value.map(item => ({
    value: item.LOC_CD,
    label: `${item.LOC_CD} - ${item.loc_nm}`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let filtered = optimizationData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  const groups = [...new Set(filtered.map(item => item.GRP_NM))]
  return groups.map(group => ({ value: group, label: group }))
})

const uniqueDepartments = computed(() => {
  let filtered = optimizationData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  const departments = [...new Set(filtered.map(item => item.DPT_NM))]
  return departments.map(dept => ({ value: dept, label: dept }))
})

const uniqueClasses = computed(() => {
  let filtered = optimizationData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (Array.isArray(selectedDepartments.value) && selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }
  const classes = [...new Set(filtered.map(item => item.CLSS_NM))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = optimizationData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (Array.isArray(selectedDepartments.value) && selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }
  if (Array.isArray(selectedClasses.value) && selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.CLSS_NM))
  }
  const subClasses = [...new Set(filtered.map(item => item.SUB_CLSS_NM))]
  return subClasses.map(subCls => ({ value: subCls, label: subCls }))
})
const uniqueRecommendations = computed(() => {
  const recommendations = ['Increase', 'Decrease', 'No Change']
  return recommendations.map(rec => ({ value: rec, label: rec }))
})
const formattedMetric = computed(() => {
  let performanceMetric = sessionStorage.getItem('performance_metric')
  if (performanceMetric === 'REVENUE') {
    performanceMetric = 'Revenue'
  } else if (performanceMetric === 'GMV') {
    performanceMetric = 'GMV'
  } else {
    performanceMetric = 'Metric'
  }
  return performanceMetric
})
const completeOptimization = async () => {
  try {
    await axios.post('scenario/completeOptimization/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
    })
    router.push({ name: 'HomePage' })
  } catch (err) {
    console.error('Error completing optimization:', err)
  }
}

const changePage = async (newPage: number) => {
  if (newPage < 1 || newPage > totalPages.value) return

  currentPage.value = newPage

  await fetchOptimizationData(newPage, rowsPerPage)
}



const saveOptimiserProgress = async (action) => {
  try {
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep ,
      progress_page: stepsStore.currentStep 
    })
    if (action === 'next') {
      stepsStore.goToNextStep()
    } else if (action === 'prev') {
      stepsStore.goToPreviousStep()
    }
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}
async function downloadCsv() {
  const concept = sessionStorage.getItem('concept');
  const territory = sessionStorage.getItem('territory_name');
  const scenario_id = sessionStorage.getItem('scenario_id');

  if (!concept || !territory || !scenario_id) {
    return;
  }

  try {
    // Make POST request to the backend
    const response = await axios.post('scenario/downloadOptimizationSummary/', {
      concept,
      territory,
      scenario_id
    });

    const source = response.data.data;

    if (!source || source.length === 0) {
      return;
    }

    // Build CSV from your `tableColumns` config
    const headers = tableColumns.map(col => col.label);
    const keys = tableColumns.map(col => col.key);

    const csvRows = [
      headers.join(','), // Header row
      ...source.map(row =>
        keys.map(key => {
          const colConfig = tableColumns.find(col => col.key === key);
          const isNumeric = colConfig?.numeric || false;
          const formatted = formatValue(row[key], isNumeric, key);

          const safeVal = typeof formatted === 'string' && /[,\n"]/.test(formatted)
            ? `"${String(formatted).replace(/"/g, '""')}"`
            : formatted;

          return safeVal ?? '';
        }).join(',')
      )
    ];

    // Create and trigger file download
    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `optimization_summary_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download error:', error);
  }
}

</script>

<template>
  <div class="flex flex-col h-full">
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
        <span class="text-gray-700">Running Optimizer...</span>
      </div>
    </div>
    <div class="flex-1 overflow-auto p-4 sm:p-6 lg:p-8 ">
      <div class="">
        <div class="bg-white rounded-lg shadow-sm p-2 mb-2">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Store</label>
              <DynamicFilter v-model="selectedStores" :multiselect="false" label="Store" placeholder="Select Stores"
                :options="uniqueStores" :searchable="(uniqueStores?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnStoreChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Groups</label>
              <DynamicFilter v-model="selectedGroups" :multiselect="true" label="Groups" placeholder="Select Groups"
                :options="uniqueGroups" :searchable="(uniqueGroups?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnGroupChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Departments</label>
              <DynamicFilter v-model="selectedDepartments" :multiselect="true" label="Departments"
                placeholder="Select Departments" :options="uniqueDepartments"
                :searchable="(uniqueDepartments?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnDepartmentChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Classes</label>
              <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Classes" placeholder="Select Classes"
                :options="uniqueClasses" :searchable="(uniqueClasses?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnClassChange()" />
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-2">
            <!-- Sub Classes -->
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Sub Classes</label>
              <DynamicFilter v-model="selectedSubClasses" :multiselect="true" label="Sub Classes"
                placeholder="Select Sub Classes" :options="uniqueSubClasses"
                :searchable="(uniqueSubClasses?.length || 0) > 10" variant="secondary" size="sm" class="w-full" />
            </div>

            <!-- Recommendation -->
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Recommendation</label>
              <DynamicFilter v-model="selectedRecommendation" :multiselect="true" label="Recommendation"
                placeholder="Recommendation" :options="uniqueRecommendations"
                :searchable="(uniqueRecommendations?.length || 0) > 10" variant="secondary" size="sm" class="w-full" />
            </div>

            <!-- Buttons (span 2 columns) -->
            <div class="col-span-2 flex items-end gap-3">
              <button @click="clearAllFilters"
                class="px-4 py-2 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
                Clear All
              </button>
              <button @click="applyFilters()"
                class="px-2 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-green-600 hover:bg-green-700 text-white focus:ring-green-500">
                Apply Filters
              </button>
              <button @click="showProductivityModal = true"
                class="px-2 py-2 h-10 text-sm font-medium border border-green-600 text-green-600 rounded-md focus:outline-none focus:ring-2 hover:bg-green-50 focus:ring-green-500">
                Saturation Curves
              </button>
              <button v-if="Object.keys(changedRecords).length > 0" @click="reRunOptimizer('optimization')"
                class="whitespace-nowrap px-2 py-2 h-10 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Re-Run Optimizer
              </button>
              <button @click="downloadCsv()"
  class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-green-600 hover:bg-green-700 text-white focus:ring-green-500 flex items-center gap-2">
  <Download class="h-4 w-4" />
  Download
</button>
            </div>
          </div>

        </div>
      </div>
      <div class="p-2 max-w-7xl mx-auto">
        <!-- Search -->
        <div class="mb-2 flex justify-end items-center gap-4">
          <div class="flex items-center">
            <h4 class="font-bold flex items-center gap-2">
              Optimized <span>{{ formattedMetric }}</span>
              <div class="relative group">
                <button type="button"
                  class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
                  tabindex="0">
                  i
                </button>
                <div
                  class="absolute left-1/2 -translate-x-1/2 mt-2 w-64 rounded-lg bg-gray-700 text-white text-xs p-3 shadow-lg z-50 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none group-hover:pointer-events-auto group-focus-within:pointer-events-auto transition-opacity"
                  style="pointer-events: none;">
                  <div>Optimized Metric = <span class="font-mono">Optimized Metric per day × Evaluation days</span>
                  </div>
                </div>
              </div>
              :
            </h4>
            <div v-for="store in [].concat(selectedStores)" :key="store">
              <span class="bg-green-100 text-green-700 font-semibold px-2 py-1 rounded ml-2">
                {{ Number(upliftData.find(item => item.loc_cd === store)?.evaluation_period_new_matric ?? 0).toLocaleString('en-US', { maximumFractionDigits: 0 }) }} <span
                  class="text-xs">AED</span>
              </span>
            </div>
          </div>
          <div class="flex">
            <h3 class="font-bold">
              Store <span>{{ formattedMetric }}</span> Uplift:
            </h3>
            <div v-for="store in [].concat(selectedStores)" :key="store">

              <span class="bg-green-100 text-green-700 font-semibold px-2 py-1 rounded ml-2">
                {{(upliftData.find(item => item.loc_cd === store)?.pct_lift)?.toFixed(2)}}%
              </span>
            </div>
          </div>
          <!-- <div>
            <span class="text-sm text-gray-500">Showing {{ optimizationData.length }} records</span>
          </div> -->
        </div>

        <!-- Table -->
        <div class="overflow-x-auto bg-white shadow-lg rounded-lg">
          <table class="min-w-full border-collapse">
            <thead>
              <tr class="bg-gray-100 text-center text-sm font-semibold text-gray-700 whitespace-nowrap">
                <th v-for="col in tableColumns" :key="col.key" class="px-4 py-3 border-b border-r last:border-r-0">
                  {{ col.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in paginatedData" :key="row.LOC_CD + '-' + row.SUB_CLSS_NM"
                class="hover:bg-gray-50 transition">
                <td v-for="col in tableColumns" :key="col.key"
                  class="px-4 py-3 border-b border-r last:border-r-0 text-sm text-center whitespace-nowrap">
                  <!-- Editable fields -->
                  <template v-if="col.key === 'lm_delta' || col.key === 'space_change_precent'">
                    <template v-if="isEditing(row, col.key)">
                      <button @click="saveEdit(row, col.key)" class="text-green-600 hover:text-green-800" title="Save">
                        ✔
                      </button>
                      <input type="number" step="1" v-model.number="row[col.key]"
                        class="w-24 px-2 py-1 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mr-1" />
                    </template>
                    <template v-else>
                      <div class="flex items-center">
                        <button @click="startEdit(row, col.key)" class="ml-2 mr-1 text-blue-600 hover:text-blue-800"
                          title="Edit">
                          ✎
                        </button>
                        <span>{{ formatValue(row[col.key],false ,col.key) }}</span>
                      </div>
                    </template>
                  </template>
                  <template v-else-if="col.key === `${metricShortNm}_change_percent`">
                    <span>
                      {{ calcMetricChangePercent(row) }}%
                    </span>
                  </template>
                  <template v-else-if="col.key === 'Final_Action'">
                    <span :class="recommendationClass(row.Final_Action)">
                      {{ capitalizeRecommendation(row.Final_Action) }}
                    </span>
                  </template>
                  <!-- Normal fields -->
                  <template v-else>
                    {{ formatValue(row[col.key], col.numeric, col.key) }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->

        <div class="flex justify-between items-center mt-4">
  <button
    :class="[
      'px-3 py-1 rounded text-sm transition-colors',
      currentPage === 1
        ? 'bg-gray-300 text-white cursor-not-allowed'
        : 'bg-gray-500 hover:bg-gray-600 text-white'
    ]"
    :disabled="currentPage === 1"
    @click="changePage(currentPage - 1)">
    Prev
  </button>

  <span class="text-sm font-medium">Page {{ currentPage }} of {{ totalPages }}</span>

  <button
    :class="[
      'px-3 py-1 rounded text-sm transition-colors',
      currentPage === totalPages
        ? 'bg-gray-300 text-white cursor-not-allowed'
        : 'bg-green-500 hover:bg-gray-600 text-white'
    ]"
    :disabled="currentPage === totalPages"
    @click="changePage(currentPage + 1)">
    Next
  </button>
</div>

        <!-- Debug: Show changed records -->
        <!-- <div class="mt-6 bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold text-gray-700 mb-2">Changed Records</h3>
          <pre class="text-xs text-gray-600">{{ changedRecords }}</pre>
        </div> -->
      </div>
    </div>
    <AlertPage
      :message="alertMessage"
      :header="headerMessage"
      :visible="showAlert"
      @ok="handleOk"
      @cancel="handleCancel"
    />
    <!-- Productivity Charts Modal -->
    <div v-if="showProductivityModal"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-lg w-[95vw] h-[95vh] max-w-[1400px] overflow-hidden flex flex-col">
        <div class="flex items-center justify-between border-b px-4 py-2">
          <h3 class="text-lg font-semibold">Saturation Analysis</h3>
          <button @click="showProductivityModal = false" class="text-gray-600 hover:text-gray-900">✕</button>
        </div>
        <div class="flex-1 overflow-auto p-2">
          <ProductivityChartsDashboard :key="showProductivityModal ? 'prod-open' : 'prod-closed'" />
        </div>
      </div>
    </div>
  </div>
</template>