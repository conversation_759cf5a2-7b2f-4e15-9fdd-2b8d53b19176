@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: #213547;
  background-color: #F8FAFC;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: "SFProText";
  font-weight: 700;
  src: url("./assets/fonts/SFProText-Bold.woff2") format("woff2"),
       url("./assets/fonts/SFProText-Bold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "SFProText";
  font-weight: 600;
  src: url("./assets/fonts/SFProText-Semibold.woff2") format("woff2"),
       url("./assets/fonts/SFProText-Semibold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "SFProText";
  font-weight: 500;
  src: url("./assets/fonts/SFProText-Medium.woff2") format("woff2"),
       url("./assets/fonts/SFProText-Medium.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "SFProText";
  font-weight: 400;
  src: url("./assets/fonts/SFProText-Regular.woff2") format("woff2"),
       url("./assets/fonts/SFProText-Regular.woff") format("woff");
  font-display: swap;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  /* font-family: "SFProText", -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; */
  font-weight: 400;
}

#app {
  width: 100%;
  height: 100vh;
}

@layer components {
  .filter-dropdown {
    @apply bg-white border border-gray-300 text-sm rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500;
  }

  .filter-date {
    @apply bg-white border border-gray-300 text-sm rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500;
  }

  .filter-btn {
    @apply bg-[#7DD3FC] hover:bg-[#38BDF8] text-white font-medium py-2 px-6 rounded-md transition-colors;
  }
}



.bg-gradient-header {
  background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
}
.multiselect__option:hover {
  background-color: #E5F5E4 !important;
  color: black !important;
}
.multiselect__option.multiselect__option--selected{
  background-color:  #E5F5E4 !important;
}
.multiselect__option.multiselect__option--selected:hover {
  background-color: #fc3737 !important;
  color: white !important;
}
.multiselect__option:first-child {
  background-color: white ;
  color: black ;
}
.multiselect__option {

    display: block;
    font-size: small;
    min-height: 25px;
    /* line-height: 16px; */
    text-decoration: none;
    text-transform: none;
    vertical-align: middle;
    position: relative;
    cursor: pointer;
    white-space: nowrap;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 5px;
    padding-bottom: 5px;
}
.multiselect__input, .multiselect__single{
  height: 20px;
  border-color: black;
  border-radius: 8px;
}
.multiselect__select{
  border-radius: 8px;
  width: 30px;
  padding: 8px 25px 0 8px;
}
.multiselect__input,
.multiselect__single {
position: relative;
display: inline-block;
min-height: 16px;
font-size: inherit;
line-height: 20px;
border: none;
/* border-radius: 8px; */
background: #fff;
padding: 0 0 0 5px;
width: calc(100%);
transition: border 0.1s ease;
box-sizing: border-box;
margin-bottom: 8px;
vertical-align: top;
}
.multiselect__tag {
font-size: smaller;
position: relative;
display: inline-block;
padding: 4px;
border-radius: 8px;
margin-right: 10px;
color: black;
line-height: 1;
background: #E5F5E4;
margin-bottom: 5px;
white-space: nowrap;
overflow: hidden;
max-width: 100%;
text-overflow: ellipsis;
}
.multiselect__option {
  display: block;
  /* padding: 12px; */
  min-height: 40px;
  /* border-radius: 8px; */
  line-height: 16px;
  text-decoration: none;
  text-transform: none;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
  padding-left: 12px;
padding-right: 12px;
padding-top: 5px;
padding-bottom: 5px;
}

.multiselect__placeholder {
color: #adadad;
display: inline-block;
margin-bottom: 5px;
font-size: smaller;
}

.multiselect__option--highlight::after {
  display: none !important;
  content: none !important;
}
