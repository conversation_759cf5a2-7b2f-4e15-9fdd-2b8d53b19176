<template>
  <div class="p-1 space-y-2">
    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border px-4 py-2">
      <div class="flex gap-3">
        <div class="w-full sm:w-1/4">
          <label class="text-sm font-semibold text-gray-700 mb-1 block">Cluster</label>
          <DynamicFilter v-model="selectedCluster" :multiselect="false" label="Cluster" placeholder="Select Cluster"
            :options="clusterOptions || []" value-key="code" label-key="name" :searchable="false" variant="outline"
            size="sm" :close-on-select="true" />
        </div>
        <div class="w-full sm:w-1/4">
          <label class="text-sm font-semibold text-gray-700 mb-1 block">Store</label>
          <DynamicFilter v-model="selectedLocation" :multiselect="true" label="Store" placeholder="Select Stores"
            :options="locationOptions || []" value-key="code" label-key="name" :searchable="true" variant="outline"
            :close-on-select="false" size="sm" />
        </div>
        <div class="w-full sm:w-1/4">
          <label class="text-sm font-semibold text-gray-700 mb-1 block">Subclass</label>
          <DynamicFilter v-model="selectedSubclass" :multiselect="false" label="Subclass" placeholder="Select Subclass"
            :options="subclassOptions || []" value-key="code" label-key="name" :searchable="true"
            :close-on-select="true" variant="outline" size="sm" />
        </div>
        <div class="w-full sm:w-1/4 flex items-end">
          <button @click="resetFilters"
            class="px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md border transition-colors">
            Reset Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Chart Section -->
    <div class="bg-white rounded-lg shadow-sm border px-6 py-2">
      <!-- Chart Header -->
      <div class="mb-2">
        <h3 class="text-sm font-bold text-gray-800 mb-2">
          {{ chartTitle }}
        </h3>
        <!-- <div class="text-sm text-gray-600">
          Revenue per Day vs Linear Meter Analysis
        </div> -->
      </div>

      <!-- Chart Options -->
      <div class="flex items-center justify-between mb-2 flex-wrap gap-4">
        <!-- Legend with counts -->
        <div class="flex gap-6 flex-wrap">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full" style="background: linear-gradient(135deg, #10b981, #059669)"></div>
            <span class="text-xs text-gray-700 font-medium">Non Outlier ({{ nonOutlierCount }})</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full" style="background: linear-gradient(135deg, #f59e0b, #d97706)"></div>
            <span class="text-xs text-gray-700 font-medium">Minor Outlier ({{ minorOutlierCount }})</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full" style="background: linear-gradient(135deg, #ef4444, #dc2626)"></div>
            <span class="text-xs text-gray-700 font-medium">Major Outlier ({{ majorOutlierCount }})</span>
          </div>
        </div>
      </div>

      <!-- Chart Container -->
      <div class="relative">
        <div ref="chartContainer" style="width: 100%; height: 300px;" class="rounded-lg border"></div>
        <div v-if="!filteredData.length"
          class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-gray-500 text-center">
            <div class="text-4xl mb-4">📊</div>
            <div class="text-lg font-medium">No data available</div>
            <div class="text-sm">Please adjust your filters to see data</div>
          </div>
        </div>
      </div>

      <!-- Data Summary Cards -->
      <div v-if="filteredData.length" class="mt-2 grid grid-cols-4 md:grid-cols-4 gap-2">
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-2 rounded-md border border-blue-200">
          <div class="flex justify-between text-sm text-blue-600 font-medium">
            <span>Total Records:</span>
            <span class="font-bold text-blue-800">{{ filteredData.length }}</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-green-50 to-green-100 p-2 rounded-md border border-green-200">
          <div class="flex justify-between text-sm text-green-600 font-medium">
            <span>Avg Revenue/Day:</span>
            <span class="font-bold text-green-800">{{ averageRevenue }}</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-2 rounded-md border border-purple-200">
          <div class="flex justify-between text-sm text-purple-600 font-medium">
            <span>Avg Linear Meter:</span>
            <span class="font-bold text-purple-800">{{ averageLinearMeter }}</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-pink-50 to-pink-100 p-2 rounded-md border border-pink-200">
          <div class="flex justify-between text-sm text-pink-600 font-medium">
            <span>Outlier Rate:</span>
            <span class="font-bold text-pink-800">{{ outlierRate }}%</span>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, nextTick, onBeforeUnmount } from 'vue'
import DynamicFilter from "./common/DynamicFilter.vue"
import * as echarts from 'echarts'

// Props
const props = defineProps({
  originalRows: {
    type: Array,
    default: () => []
  }
})

// Reactive variables
const selectedSubclass = ref(null)
const selectedLocation = ref([])
const selectedCluster = ref(null)
const chartContainer = ref(null)
let chartInstance = null

// Chart colors for different outlier types
const OUTLIER_CHART_COLORS = {
  NON_OUTLIER: {
    color: ['#10b981', '#059669'],
    symbolSize: 8
  },
  MINOR_OUTLIER: {
    color: ['#f59e0b', '#d97706'],
    symbolSize: 10
  },
  MAJOR_OUTLIER: {
    color: ['#ef4444', '#dc2626'],
    symbolSize: 12
  }
}

// Computed options for dropdowns
const subclassOptions = computed(() => {
  const uniqueSubclasses = [...new Set(props.originalRows.map(d => d.subclass))]
  return uniqueSubclasses.map(d => ({
    code: d,
    name: d
  }))
})

const locationOptions = computed(() => {
  let filteredRows = props.originalRows

  // Filter by cluster if selected
  if (selectedCluster.value !== null) {
    filteredRows = filteredRows.filter(d => d.new_cluster === selectedCluster.value)
  }

  // Filter by subclass if selected
  if (selectedSubclass.value) {
    filteredRows = filteredRows.filter(d => d.subclass === selectedSubclass.value)
  }

  const uniqueLocations = [...new Map(filteredRows.map(d => [d.storeId, d])).values()]

  return uniqueLocations.map(d => ({
    code: d.storeId,
    name: `${d.storeId} - ${d.storeName}`
  }))
})

const clusterOptions = computed(() => {
  const uniqueClusters = [...new Set(props.originalRows.map(row => row.new_cluster))].sort((a, b) => a - b)
  console.log(uniqueClusters, props.originalRows)
  return uniqueClusters.map(c => ({
    code: c,
    name: `Cluster ${c}`
  }))
})

// Filtered data based on selections
const filteredData = computed(() => {
  let filtered = props.originalRows

  if (selectedCluster.value !== null) {
    filtered = filtered.filter(d => d.new_cluster === selectedCluster.value)
  }

  if (selectedSubclass.value) {
    filtered = filtered.filter(d => d.subclass === selectedSubclass.value)
  }

  if (selectedLocation.value && selectedLocation.value.length > 0) {
    filtered = filtered.filter(d => selectedLocation.value.includes(d.storeId))
  }

  return filtered
})

// Chart title
const chartTitle = computed(() => {
  let title = 'Revenue per Day vs Linear Meter'

  if (selectedSubclass.value) {
    title = `${selectedSubclass.value} - ${title}`
  }

  if (selectedCluster.value !== null) {
    title = `Cluster ${selectedCluster.value} - ${title}`
  }

  if (selectedLocation.value && selectedLocation.value.length === 1) {
    const store = props.originalRows.find(d => d.storeId === selectedLocation.value[0])
    if (store) {
      title = `${store.storeId} (${store.storeName}) - ${title}`
    }
  } else if (selectedLocation.value && selectedLocation.value.length > 1) {
    title = `${selectedLocation.value.length} Stores - ${title}`
  }

  return title
})

// Statistics
const nonOutlierCount = computed(() =>
  filteredData.value.filter(d => d.outlierFinalStatus === 'NON_OUTLIER').length
)

const minorOutlierCount = computed(() =>
  filteredData.value.filter(d => d.outlierFinalStatus === 'MINOR_OUTLIER').length
)

const majorOutlierCount = computed(() =>
  filteredData.value.filter(d => d.outlierFinalStatus === 'MAJOR_OUTLIER').length
)

const averageRevenue = computed(() => {
  if (!filteredData.value.length) return '0'
  const avg = filteredData.value.reduce((sum, d) => sum + d.perDay, 0) / filteredData.value.length
  return avg.toFixed(1)
})

const averageLinearMeter = computed(() => {
  if (!filteredData.value.length) return '0'
  const avg = filteredData.value.reduce((sum, d) => sum + d.totalLm, 0) / filteredData.value.length
  return avg.toFixed(1)
})

const selectedStoresCount = computed(() => {
  if (!selectedLocation.value || selectedLocation.value.length === 0) {
    return new Set(filteredData.value.map(d => d.storeId)).size
  }
  return selectedLocation.value.length
})

const outlierRate = computed(() => {
  if (!filteredData.value.length) return '0'
  const outliers = filteredData.value.filter(d => d.outlierFinalStatus !== 'NON_OUTLIER').length
  return ((outliers / filteredData.value.length) * 100).toFixed(1)
})

// ECharts functions
function initChart() {
  if (!chartContainer.value) return

  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartContainer.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })

  // Set up resize handler
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

function updateChart() {
  if (!chartInstance || !filteredData.value.length) return

  // Prepare data for each outlier type
  const seriesData = {
    NON_OUTLIER: [],
    MINOR_OUTLIER: [],
    MAJOR_OUTLIER: []
  }

  filteredData.value.forEach(d => {
    const dataPoint = {
      value: [d.totalLm, d.perDay],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0, color: OUTLIER_CHART_COLORS[d.outlierFinalStatus].color[0] },
          { offset: 1, color: OUTLIER_CHART_COLORS[d.outlierFinalStatus].color[1] }
        ])
      },
      symbolSize: OUTLIER_CHART_COLORS[d.outlierFinalStatus].symbolSize,
      store: `${d.storeId} - ${d.storeName}`,
      month: formatMonth(d.month),
      status: d.outlierFinalStatus,
      lmContribution: d.lmContrib,
      suggestedLm: d.suggestedTotalLm
    }
    seriesData[d.outlierFinalStatus].push(dataPoint)
  })

  const option = {
    backgroundColor: '#ffffff',
    grid: {
      left: '12%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: 'Linear Meter (LM)',
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#374151'
      },
      axisLine: {
        lineStyle: {
          color: '#d1d5db',
          width: 2
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: 'Revenue per Day',
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#374151'
      },
      axisLine: {
        lineStyle: {
          color: '#d1d5db',
          width: 2
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
        formatter: function (value) {
          return value.toFixed(0)
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151',
        fontSize: 12
      },
      formatter: function (params) {
        const data = params.data
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #1f2937;">${data.store}</div>
            <div style="display: flex; flex-direction: column; gap: 4px;">
              <div><span style="color: #6b7280;">Linear Meter:</span> <strong>${data.value[0].toFixed(1)}</strong></div>
              <div><span style="color: #6b7280;">Revenue/Day:</span> <strong>${data.value[1].toFixed(1)}</strong></div>
              <div><span style="color: #6b7280;">Month:</span> <strong>${data.month}</strong></div>
              <div><span style="color: #6b7280;">Status:</span> <strong style="color: ${OUTLIER_CHART_COLORS[data.status].color[0]};">${data.status.replace('_', ' ')}</strong></div>
              <div><span style="color: #6b7280;">LM Contribution:</span> <strong>${data.lmContribution}</strong></div>
              <div><span style="color: #6b7280;">Suggested LM:</span> <strong>${data.suggestedLm}</strong></div>
            </div>
          </div>
        `
      }
    },
    toolbox: {
      right: 20,
      top: 20,
      feature: {
        dataZoom: {
          yAxisIndex: 'none',
          title: {
            zoom: 'Zoom',
            back: 'Reset Zoom'
          }
        },
        saveAsImage: {
          title: 'Save as Image',
          pixelRatio: 2
        }
      },
      iconStyle: {
        borderColor: '#6b7280'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none'
      },
      {
        type: 'inside',
        yAxisIndex: 0,
        filterMode: 'none'
      }
    ],
    series: [
      {
        name: 'Non Outlier',
        type: 'scatter',
        data: seriesData.NON_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(16, 185, 129, 0.5)'
          }
        }
      },
      {
        name: 'Minor Outlier',
        type: 'scatter',
        data: seriesData.MINOR_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(245, 158, 11, 0.5)'
          }
        }
      },
      {
        name: 'Major Outlier',
        type: 'scatter',
        data: seriesData.MAJOR_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(239, 68, 68, 0.5)'
          }
        }
      }
    ],
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  chartInstance.setOption(option, true)
}

function resetZoom() {
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }
}

function downloadChart() {
  if (chartInstance) {
    const url = chartInstance.getDataURL({
      pixelRatio: 2,
      backgroundColor: '#ffffff'
    })
    const link = document.createElement('a')
    link.download = `outlier-analysis-${new Date().toISOString().split('T')[0]}.png`
    link.href = url
    link.click()
  }
}

function formatMonth(monthStr) {
  const year = +monthStr.slice(0, 4)
  const month = +monthStr.slice(4, 6) - 1
  const date = new Date(year, month)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric'
  })
}

function resetFilters() {
  selectedLocation.value = []
  selectedCluster.value = clusterOptions.value.length > 0 ? clusterOptions.value[0].code : null
  selectedSubclass.value = subclassOptions.value.length > 0 ? subclassOptions.value[0].code : null
}

// Lifecycle hooks
onMounted(async () => {
  // Set default selections
  if (clusterOptions.value.length > 0 && selectedCluster.value === null) {
    selectedCluster.value = clusterOptions.value[0].code
  }

  if (subclassOptions.value.length > 0 && !selectedSubclass.value) {
    selectedSubclass.value = subclassOptions.value[0].code
  }

  await nextTick()
  initChart()
  updateChart()
})

// Watchers
watch([selectedSubclass, selectedLocation, selectedCluster], async () => {
  await nextTick()
  updateChart()
}, { deep: true })

// Cleanup
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', () => { })
})
</script>

<style scoped>
/* Custom multiselect styles */
:deep(.multiselect) {
  min-height: 38px;
}

:deep(.multiselect__single) {
  font-size: 14px;
  margin-bottom: 0;
  padding: 0 8px;
}

:deep(.multiselect__tags) {
  min-height: 38px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

:deep(.multiselect__placeholder) {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 0;
  padding-top: 0;
}

:deep(.multiselect__tag) {
  background: #3b82f6;
  color: white;
  font-size: 12px;
}

:deep(.multiselect__option--highlight) {
  background: #3b82f6;
  color: white;
}

:deep(.multiselect__option--selected) {
  background: #1e40af;
  color: white;
}

/* Custom button styles */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Chart container styling */
.chart-container {
  border-radius: 8px;
  overflow: hidden;
}
</style>