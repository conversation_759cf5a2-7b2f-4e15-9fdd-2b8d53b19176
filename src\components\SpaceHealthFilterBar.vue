<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-4">
      <div>
        <label class="ml-2 text-sm font-medium text-gray-700">Store<span class="text-red-500 ml-1">*</span></label>
        <DynamicFilter v-model="selectedStores" :multiselect="false" :close-on-select="!multiselect" label="Store" placeholder="Store"
          :options="uniqueStores" :searchable="(uniqueStores?.length || 0) > 10" variant="secondary" size="sm" />
      </div>
      <div>
        <label class="ml-2 text-sm font-medium text-gray-700">Groups</label>
        <DynamicFilter v-model="selectedGroups" :multiselect="true"  label="Group" placeholder="Group"
          :options="uniqueGroups" :searchable="(uniqueGroups?.length || 0) > 10" variant="secondary" size="sm" />
      </div>
      <div>
        <label class="ml-2 text-sm font-medium text-gray-700">Departments</label>
        <DynamicFilter v-model="selectedDepartments" :multiselect="true" label="Department" placeholder="Department"
          :options="uniqueDepartments" :searchable="(uniqueDepartments?.length || 0) > 10" variant="secondary" size="sm" />
      </div>
      <div>
        <label class="ml-2 text-sm font-medium text-gray-700">Classes</label>
        <DynamicFilter v-model="selectedClasses" :multiselect="true"  label="Class" placeholder="Class"
          :options="uniqueClasses" :searchable="(uniqueClasses?.length || 0) > 10" variant="secondary" size="sm" />
      </div>
    </div>
    <!-- Second row of grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 items-end">
      <!-- SubClass Dropdown -->
      <div>
        <label class="ml-2 text-sm font-medium text-gray-700">Sub Classes</label>
        <DynamicFilter v-model="selectedSubClasses" :multiselect="true"  label="SubClass" placeholder="SubClass"
          :options="uniqueSubClasses" :searchable="(uniqueSubClasses?.length || 0) > 10" variant="secondary" size="sm" />
      </div>
      <!-- Date Range -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">From Month <span class="text-red-500 ml-1">*</span></label>
        <input v-model="fromMonth" :min="props.monthRange.start" :max="props.monthRange.end" type="month"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">To Month <span class="text-red-500 ml-1">*</span></label>
        <input v-model="toMonth" :min="props.monthRange.start" :max="props.monthRange.end" type="month"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"/>
      </div>
      <!-- Clear All & Apply Filters Buttons (together in one column) -->
      <div class="flex gap-4">
        <button @click="clearAllFilters"
          class="px-4 py-2 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
          Clear All
        </button>
        <button @click="emitFilters"
          class="px-4 py-2 h-10 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
          Apply Filters
        </button>

      <!-- Download CSV Button aligned right -->
        <button
          @click="onDownloadCSV"
          class="flex items-center gap-2 px-5 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-semibold rounded-lg shadow transition"
        >
          <Download class="w-4 h-6" />
        </button>
      </div>
    </div>


  </div>
</template>


<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import axios from 'axios'
import DynamicFilter from './common/DynamicFilter.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { Download } from 'lucide-vue-next'

const stepsStore = useStepsStore()
const currentScenario = computed(() => stepsStore.getScenarioData)


const props = defineProps({
  gdcsData: { type: Array, default: () => [] },
  tableData: { type: Array, default: () => [] },
  monthRange: { type: Object, default: () => ({ start: '', end: '' }) }
})
// Define emits
const emit = defineEmits(['apply-filters', 'clear-filters'])

// v-model props
const selectedStores = ref([])
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubClasses = ref([])
const fromMonth = ref('')
const toMonth = ref('')
// Unique options for filters from parent data
const uniqueStores = computed(() =>
  [...new Map(props.gdcsData.map(item => [item.loc_cd, { value: item.loc_cd, label: `${item.loc_cd} - ${item.loc_nm}` }])).values()]
)
const uniqueGroups = computed(() =>
  [...new Set(props.gdcsData.map(item => item.grp_nm))].map(g => ({ value: g, label: g }))
)
const uniqueDepartments = computed(() =>
  [...new Set(props.gdcsData.map(item => item.dpt_nm))].map(d => ({ value: d, label: d }))
)
const uniqueClasses = computed(() =>
  [...new Set(props.gdcsData.map(item => item.clss_nm))].map(c => ({ value: c, label: c }))
)
const uniqueSubClasses = computed(() =>
  [...new Set(props.gdcsData.map(item => item.sub_clss_nm))].map(s => ({ value: s, label: s }))
)

const scenarioDetails = reactive({
  gdcsData: [],
  concept: '',
  scenario_id: null
})

onMounted(async () => {
  scenarioDetails.concept = currentScenario.value.CNCPT_NM
  scenarioDetails.scenario_id = currentScenario.value.id
})

watch(
  () => props.gdcsData,
  (newVal) => {
    if (newVal.length > 0 && selectedStores.value.length === 0) {
      const firstStore = newVal[0].loc_cd
      selectedStores.value = firstStore
    }
  },
  { immediate: true } 
)


const clearAllFilters = () => {
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  fromMonth.value = props.monthRange.start
  toMonth.value = props.monthRange.end
  emit('clear-filters')  // optional if Dashboard needs to reset table
}

const emitFilters = () => {
  emit('apply-filters', {
    store_id: selectedStores.value,
    group: selectedGroups.value,
    department: selectedDepartments.value,
    class: selectedClasses.value,
    sub_class: selectedSubClasses.value,
    from_month: fromMonth.value || props.monthRange.start,
    to_month: toMonth.value || props.monthRange.end
  })
}
function formatNumber(value, decimals = 0) {
  if (value == null || value === "") return "-";
  // make a numeric conversion robust to strings with commas
  const num = Number(String(value).replace(/,/g, ""));
  if (isNaN(num)) return "-";
  return num.toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
}

function formatCurrency(value, decimals = 0) {
  if (value == null || value === "") return "-";
  const num = Number(String(value).replace(/,/g, ""));
  if (isNaN(num)) return "-";
  return new Intl.NumberFormat("en-AE", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num);
}

function getFieldValue(value) {
  return value == null || value === "" ? "-" : value;
}

function escapeCsvValue(value) {
  if (value == null) return '""';
  const s = String(value);
  if (/[",\n\r]/.test(s)) {
    return `"${s.replace(/"/g, '""')}"`;
  }
  return s;
}

// --- columns remain same but formatters can accept (value, row, key) if needed ---
const csvColumnConfig = [
  { key: "locCode", label: "Store ID", formatter: (v) => getFieldValue(v) },
  { key: "locNm", label: "Store Name", formatter: (v) => getFieldValue(v) },
  { key: "group", label: "Group", formatter: (v) => getFieldValue(v) },
  { key: "department", label: "Department", formatter: (v) => getFieldValue(v) },
  { key: "class", label: "Class", formatter: (v) => getFieldValue(v) },
  { key: "subClass", label: "Subclass", formatter: (v) => getFieldValue(v) },
  { key: "lm", label: "LM", formatter: (v) => formatNumber(v, 2) },
  { key: "sqft", label: "Sqft", formatter: (v) => formatNumber(v, 2) },
  { key: "lmRank", label: "LM Rank", formatter: (v) => getFieldValue(v) },
  { key: "sqftRank", label: "Sqft Rank", formatter: (v) => getFieldValue(v) },
  { key: "fixtureDensity", label: "Fixture Density", formatter: (v) => formatNumber(v, 2) },
  { key: "lmCont", label: "LM Cont", formatter: (v) => (formatNumber(v, 0) === "-" ? "-" : formatNumber(v, 0) + "%") },
  { key: "sqftCont", label: "Sqft Cont", formatter: (v) => (formatNumber(v, 0) === "-" ? "-" : formatNumber(v, 0) + "%") },

  // Stock Metrics
  { key: "optionCount", label: "Option Count", formatter: (v) => formatNumber(v, 0) },
  { key: "optionDensity", label: "Option Density", formatter: (v) => formatNumber(v, 2) },
  { key: "sohQty", label: "SOH Qty", formatter: (v) => formatNumber(v, 0) },
  { key: "stockDensity", label: "Stock Density", formatter: (v) => formatNumber(v, 2) },

  // Sales Metrics
  { key: "revenue", label: "Revenue", formatter: (v) => formatCurrency(v, 0) },
  { key: "gmv", label: "GMV", formatter: (v) => formatCurrency(v, 0) },
  { key: "revenuePerDay", label: "Revenue per day", formatter: (v) => formatCurrency(v, 0) },
  { key: "gmvPerDay", label: "GMV per day", formatter: (v) => formatCurrency(v, 0) },

  // Productivity
  { key: "revPerLM", label: "Rev per LM", formatter: (v) => formatCurrency(v, 0) },
  { key: "gmvPerLM", label: "GMV per LM", formatter: (v) => formatCurrency(v, 0) },
  { key: "revPerSqft", label: "Rev per Sqft", formatter: (v) => formatCurrency(v, 0) },
  { key: "gmvPerSqft", label: "GMV per Sqft", formatter: (v) => formatCurrency(v, 0) },
  { key: "revPerLmPerDay", label: "Rev per LM per day", formatter: (v) => formatCurrency(v, 2) },
  { key: "revPerSqftPerDay", label: "Rev per Sqft per day", formatter: (v) => formatCurrency(v, 2) },
  { key: "gmvPerLmPerDay", label: "GMV per LM per day", formatter: (v) => formatCurrency(v, 2) },
  { key: "gmvPerSqftPerDay", label: "GMV per Sqft per day", formatter: (v) => formatCurrency(v, 2) },
  { key: "revPerLmPerDayRank", label: "Rev per LM per day (Rank)", formatter: (v) => getFieldValue(v) },
  { key: "revPerSqftPerDayRank", label: "Rev per Sqft per day (Rank)", formatter: (v) => getFieldValue(v) },
  { key: "gmvPerLmPerDayRank", label: "GMV per LM per day (Rank)", formatter: (v) => getFieldValue(v) },
  { key: "gmvPerSqftPerDayRank", label: "GMV per Sqft per day (Rank)", formatter: (v) => getFieldValue(v) },

  // Inventory Health
  { key: "invCount", label: "Current Stock", formatter: (v) => formatNumber(v, 0) },
  { key: "ros", label: "Average Weekly Sales", formatter: (v) => formatNumber(v, 0) },
  { key: "cover", label: "Cover (days)", formatter: (v) => formatNumber(v, 0) },
];

// Build rows and escape properly
function buildCsvRows(data) {
  return data.map(row => {
    return csvColumnConfig.map(col => {
      const rawValue = row?.[col.key];
      const formatted = typeof col.formatter === "function"
        ? col.formatter(rawValue, row, col.key)
        : getFieldValue(rawValue);
      return escapeCsvValue(formatted);
    });
  });
}

function downloadCSV(data) {
  const headers = csvColumnConfig.map(col => escapeCsvValue(col.label)).join(",");
  const rows = buildCsvRows(data)
    .map(r => r.join(","))
    .join("\n");

  const csvContent = "\uFEFF" + [headers, rows].join("\n");
  const filename = `health_report_${new Date().toISOString().slice(0,19).replace(/[:T]/g,"-")}.csv`;

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function onDownloadCSV() {
  downloadCSV(props.tableData)
}

watch(
  () => props.monthRange,
  (newRange) => {
    fromMonth.value = newRange.start
    toMonth.value = newRange.end
  },
  { immediate: true }
)

</script>

<style scoped>
.multiselect {
  min-height: 32px;
  font-size: 0.875rem;
}
</style>