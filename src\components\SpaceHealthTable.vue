<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { spaceHealthData, SpaceHealthRow } from '../data/spaceHealthData';

// State management
const expandedGroups = ref<Set<string>>(new Set());
const expandedDepartments = ref<Map<string, Set<string>>>(new Map());
const expandedClasses = ref<Map<string, Set<string>>>(new Map());

// Computed properties
const groupRows = computed(() => spaceHealthData.slice(0, 3));

// Optimized expansion state checkers using Set/Map for O(1) lookup
const isGroupExpanded = (group: string) => expandedGroups.value.has(group);
const isDepartmentExpanded = (group: string, department: string) => 
  expandedDepartments.value.get(group)?.has(department) ?? false;
const isClassExpanded = (group: string, department: string, className: string) => 
  expandedClasses.value.get(`${group}|${department}`)?.has(className) ?? false;

// Optimized toggle functions
const toggleGroup = (group: string) => {
  const expanded = expandedGroups.value;
  if (expanded.has(group)) {
    expanded.delete(group);
  } else {
    expanded.add(group);
  }
};

const toggleDepartment = (group: string, department: string) => {
  if (!expandedDepartments.value.has(group)) {
    expandedDepartments.value.set(group, new Set());
  }
  const deptSet = expandedDepartments.value.get(group)!;
  if (deptSet.has(department)) {
    deptSet.delete(department);
  } else {
    deptSet.add(department);
  }
};

const toggleClass = (group: string, department: string, className: string) => {
  const depKey = `${group}|${department}`;
  if (!expandedClasses.value.has(depKey)) {
    expandedClasses.value.set(depKey, new Set());
  }
  const classSet = expandedClasses.value.get(depKey)!;
  if (classSet.has(className)) {
    classSet.delete(className);
  } else {
    classSet.add(className);
  }
};

// Initialize state
onMounted(() => {
  expandedGroups.value.clear();
  expandedDepartments.value.clear();
  expandedClasses.value.clear();
});

// Optimized chevron function - returns class instead of HTML string
const getChevronClass = (isExpanded: boolean) => 
  isExpanded ? 'rotate-0' : '-rotate-90';

// Constants
const allColumns = [
  'Store ID', 'Group', 'Department', 'Class', 'Sub Class', 'Sqft Exists', 'LM', 'Sqft', 
  'LM Rank', 'Sqft Rank', 'Option Count', 'Option per Sqft', 'SOH Qty', 'SOH per Sqft', 
  'Revenue(AED)', 'GMV', 'Rev per Sqft(AED)', 'GMV per LM', 'Cost', 'Avg Stock', 
  'Present ROS', 'Cover (days)', 'LM Cont', 'Sqft Cont', 'Diff'
];

// Memoized column mapping function
const columnMappings = new Map([
  ['LM Rank', 'lm2'],
  ['Sqft Rank', 'sqft2'],
  ['GMV per LM', 'gmvPerLm'],
  ['Store ID', 'storeId'],
  ['Group', 'group'],
  ['Department', 'department'],
  ['Class', 'class'],
  ['Sub Class', 'subClass'],
  ['Sqft Exists', 'sqftExists'],
  ['LM', 'lm'],
  ['Sqft', 'sqft'],
  ['Option Count', 'optionCount'],
  ['Option per Sqft', 'optionPerSqft'],
  ['SOH Qty', 'sohQty'],
  ['SOH per Sqft', 'sohPerSqft'],
  ['Revenue(AED)', 'revenue'],
  ['GMV', 'gmv'],
  ['Rev per Sqft(AED)', 'revPerSqft'],
  ['Cost', 'cost'],
  ['Avg Stock', 'avgStock'],
  ['Present ROS', 'presentRos'],
  ['Cover (days)', 'coverDays'],
  ['LM Cont', 'lmCont'],
  ['Sqft Cont', 'sqftCont'],
  ['Diff', 'diff']
]);

const colMap = (col: string): string => {
  return columnMappings.get(col) || col.toLowerCase().replace(/\s+/g, '');
};

// Optimized aggregation function with memoization potential
const aggregateTotals = (rows: SpaceHealthRow[]): Partial<SpaceHealthRow> => {
  if (rows.length === 0) return {};
  
  const sumFields = ['lm', 'sqft', 'lm2', 'sqft2', 'optionCount', 'sohQty', 'revenue', 
                     'gmv', 'cost', 'avgStock', 'coverDays', 'lmCont', 'sqftCont', 'diff'];
  const avgFields = ['optionPerSqft', 'sohPerSqft', 'revPerSqft', 'gmvPerLm', 'presentRos'];
  
  const total: any = { ...rows[0] }; // Copy first row's non-numeric fields
  
  // Sum fields
  sumFields.forEach(field => {
    total[field] = rows.reduce((sum, r) => sum + (r[field] || 0), 0);
  });
  
  // Average fields
  avgFields.forEach(field => {
    const sum = rows.reduce((sum, r) => sum + (r[field] || 0), 0);
    total[field] = Math.round((sum / rows.length) * 100) / 100; // Round to 2 decimals
  });
  
  total.sqftExists = 'Yes';
  return total;
};

// Optimized number formatting
const formatNumber = (val: number): string => {
  if (typeof val !== 'number' || isNaN(val)) return '';
  return val.toLocaleString('en-US', { 
    minimumFractionDigits: val % 1 === 0 ? 0 : 1,
    maximumFractionDigits: 1 
  });
};

const displayValue = (val: any): string => {
  if (typeof val === 'number') return formatNumber(val);
  if (val === '' || val === undefined || val === null) return '-';
  return String(val);
};

// Simplified sticky positioning
const stickyColumns = 5;
const columnWidths = [70, 140, 180, 160, 160];
const getStickyLeft = (index: number) => {
  return columnWidths.slice(0, index).reduce((sum, width) => sum + width, 0);
};
</script>

<template>
  <div class="w-full">
    <div class="max-w-full border border-gray-200 rounded-lg shadow-sm overflow-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <!-- Header -->
        <thead class="bg-primary sticky top-0">
          <tr>
            <th 
              v-for="(header, idx) in allColumns" 
              :key="header"
              :class="[
                'py-3 px-4 text-center text-xs font-bold text-black uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0',
                idx < stickyColumns ? 'sticky bg-primary' : 'min-w-[120px]'
              ]"
              :style="idx < stickyColumns ? { 
                left: getStickyLeft(idx) + 'px',
                width: columnWidths[idx] + 'px'
              } : {}"
            >
              {{ header }}
            </th>
          </tr>
        </thead>

        <tbody class="bg-white divide-y divide-gray-200">
          <template v-for="groupRow in groupRows" :key="groupRow.group">
            <!-- Group Total Row -->
            <tr 
              class="bg-white font-bold cursor-pointer hover:bg-gray-50 transition-colors duration-150"
              @click="toggleGroup(groupRow.group)"
            >
              <td 
                v-for="(col, idx) in allColumns" 
                :key="col"
                :class="[
                  'py-3 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                  idx < stickyColumns ? 'sticky bg-white' : ''
                ]"
                :style="idx < stickyColumns ? { 
                  left: getStickyLeft(idx) + 'px',
                  width: columnWidths[idx] + 'px'
                } : {}"
              >
                <!-- Store ID -->
                <div v-if="idx === 0" class="truncate" :title="displayValue(groupRow.storeId)">
                  {{ displayValue(groupRow.storeId) }}
                </div>
                <!-- Group with chevron -->
                <div v-else-if="idx === 1" class="flex items-center justify-center gap-2">
                  <svg 
                    class="w-4 h-4 transition-transform duration-200"
                    :class="getChevronClass(isGroupExpanded(groupRow.group))"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                  </svg>
                  <span class="truncate" :title="displayValue(groupRow.group) + ' Total'">
                    {{ displayValue(groupRow.group) }} Total
                  </span>
                </div>
                <!-- Other columns -->
                <div v-else-if="idx <= 4" class="truncate" :title="displayValue(groupRow[colMap(col)])">
                  {{ displayValue(groupRow[colMap(col)]) }}
                </div>
                <div v-else class="truncate" :title="displayValue(aggregateTotals([groupRow])[colMap(col)])">
                  {{ displayValue(aggregateTotals([groupRow])[colMap(col)]) }}
                </div>
              </td>
            </tr>

            <!-- Expanded Group Content -->
            <template v-if="isGroupExpanded(groupRow.group)">
              <template v-for="deptRow in groupRow.children || []" :key="deptRow.department">
                <!-- Department Total Row -->
                <tr 
                  class="bg-gray-25 font-semibold cursor-pointer hover:bg-gray-75 transition-colors duration-150"
                  @click.stop="toggleDepartment(groupRow.group, deptRow.department)"
                >
                  <td 
                    v-for="(col, idx) in allColumns" 
                    :key="col"
                    :class="[
                      'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                      idx < stickyColumns ? 'sticky bg-gray-25' : ''
                    ]"
                    :style="idx < stickyColumns ? { 
                      left: getStickyLeft(idx) + 'px',
                      width: columnWidths[idx] + 'px'
                    } : {}"
                  >
                    <div v-if="idx === 0" class="truncate pl-4" :title="displayValue(deptRow.storeId)">
                      {{ displayValue(deptRow.storeId) }}
                    </div>
                    <div v-else-if="idx === 1" class="truncate pl-4" :title="displayValue(deptRow.group)">
                      {{ displayValue(deptRow.group) }}
                    </div>
                    <div v-else-if="idx === 2" class="flex items-center justify-center gap-2">
                      <svg 
                        class="w-4 h-4 transition-transform duration-200"
                        :class="getChevronClass(isDepartmentExpanded(groupRow.group, deptRow.department))"
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                      </svg>
                      <span class="truncate" :title="displayValue(deptRow.department) + ' Total'">
                        {{ displayValue(deptRow.department) }} Total
                      </span>
                    </div>
                    <div v-else-if="idx < 5" class="truncate">-</div>
                    <div v-else class="truncate" :title="displayValue(aggregateTotals([deptRow])[colMap(col)])">
                      {{ displayValue(aggregateTotals([deptRow])[colMap(col)]) }}
                    </div>
                  </td>
                </tr>

                <!-- Expanded Department Content -->
                <template v-if="isDepartmentExpanded(groupRow.group, deptRow.department)">
                  <template v-for="classRow in deptRow.children || []" :key="classRow.class">
                    <!-- Class Total Row -->
                    <tr 
                      class="bg-gray-50 font-normal cursor-pointer hover:bg-gray-100 transition-colors duration-150"
                      @click.stop="toggleClass(groupRow.group, deptRow.department, classRow.class)"
                    >
                      <td 
                        v-for="(col, idx) in allColumns" 
                        :key="col"
                        :class="[
                          'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                          idx < stickyColumns ? 'sticky bg-gray-50' : ''
                        ]"
                        :style="idx < stickyColumns ? { 
                          left: getStickyLeft(idx) + 'px',
                          width: columnWidths[idx] + 'px'
                        } : {}"
                      >
                        <div v-if="idx === 0" class="truncate pl-8" :title="displayValue(classRow.storeId)">
                          {{ displayValue(classRow.storeId) }}
                        </div>
                        <div v-else-if="idx === 1" class="truncate pl-8" :title="displayValue(classRow.group)">
                          {{ displayValue(classRow.group) }}
                        </div>
                        <div v-else-if="idx === 2" class="truncate pl-8" :title="displayValue(classRow.department)">
                          {{ displayValue(classRow.department) }}
                        </div>
                        <div v-else-if="idx === 3" class="flex items-center justify-center gap-2">
                          <svg 
                            class="w-4 h-4 transition-transform duration-200"
                            :class="getChevronClass(isClassExpanded(groupRow.group, deptRow.department, classRow.class))"
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                          </svg>
                          <span class="truncate" :title="displayValue(classRow.class) + ' Total'">
                            {{ displayValue(classRow.class) }} Total
                          </span>
                        </div>
                        <div v-else-if="idx < 5" class="truncate">-</div>
                        <div v-else class="truncate" :title="displayValue(aggregateTotals([classRow])[colMap(col)])">
                          {{ displayValue(aggregateTotals([classRow])[colMap(col)]) }}
                        </div>
                      </td>
                    </tr>

                    <!-- Expanded Class Content -->
                    <template v-if="isClassExpanded(groupRow.group, deptRow.department, classRow.class)">
                      <tr 
                        v-for="(subRow, sIdx) in classRow.children || []" 
                        :key="subRow.subClass"
                        :class="[
                          'transition-colors duration-150 border-b border-gray-100',
                          sIdx % 2 === 0 ? 'bg-white hover:bg-green-25' : 'bg-gray-25 hover:bg-green-50'
                        ]"
                      >
                        <td 
                          v-for="(col, idx) in allColumns" 
                          :key="col"
                          :class="[
                            'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                            idx < stickyColumns ? 'sticky' : '',
                            idx < stickyColumns && sIdx % 2 === 0 ? 'bg-white' : idx < stickyColumns ? 'bg-gray-25' : ''
                          ]"
                          :style="idx < stickyColumns ? { 
                            left: getStickyLeft(idx) + 'px',
                            width: columnWidths[idx] + 'px'
                          } : {}"
                        >
                          <div 
                            v-if="idx <= 4"
                            class="truncate"
                            :class="{ 'pl-12': true }"
                            :title="displayValue(subRow[colMap(col)])"
                          >
                            {{ displayValue(subRow[colMap(col)]) }}
                          </div>
                          <div v-else class="truncate" :title="displayValue(subRow[colMap(col)])">
                            {{ displayValue(subRow[colMap(col)]) }}
                          </div>
                        </td>
                      </tr>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.875rem;
}

.rotate-0 {
  transform: rotate(0deg);
}

.-rotate-90 {
  transform: rotate(-90deg);
}

/* Ensure proper text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Custom scrollbar for better UX */
.overflow-auto::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>