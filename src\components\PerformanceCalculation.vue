<script setup lang="ts">
import { onMounted, ref, watch, nextTick, onUnmounted, computed } from 'vue';
import WeightsCalcu from './WeightsCalcu.vue';
import DynamicFilter from './common/DynamicFilter.vue'
import axios from 'axios'
import { baseURL, baseFastapiURL } from '../main';

const tableData = ref([]);
const currentPage = ref(1);
const rowsPerPage = 10;
const totalRows = ref(0);
const weightsConfigure = ref(false);
const isLoading = ref(false)
const selectedStores = ref('')
const selectedSubClasses = ref([])
const actionOptions = ["no change", "drop", "increase", "decrease"]
const isTableLoading = ref(false);
const loadingMessage = ref('');

const categoryMap = {
  'L': 'Low',
  'M': 'Medium',
  'H': 'High'
};

const mapCategory = (value: string): string => {
  return categoryMap[value] || value;
};


async function updateWeights(performanceWeights) {
  try {
    const response = await axios.post('/scenario/update_weights/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
      weights: performanceWeights,
    })
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}

const showFiltered = ref(false);
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  const source = showFiltered.value ? filteredData.value : tableData.value;
  return source.slice(start, start + rowsPerPage);
});

async function callPreFetchApi(module_name) {
  try {
    const scenario_id = sessionStorage.getItem('scenario_id');
    const response = await axios.post(`${baseFastapiURL}/run/${module_name}`, {
      SCENARIO_ID: scenario_id
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e;
  }
}

const runPerformanceApi = async () => {
  isTableLoading.value = true;
  try {
    loadingMessage.value = 'Analysing your data...';
    await callPreFetchApi('datapreparation');
    loadingMessage.value = 'Measuring saturation levels...';
    await callPreFetchApi('saturation');
    loadingMessage.value = 'Setting things up...';
    await new Promise(resolve => setTimeout(resolve, 5000));
    loadingMessage.value = 'Fetching data...';
    await fetchPerformanceData();

  } catch (e) {
    console.error('Failed to fetch performance data', e);
  } finally {
    isTableLoading.value = false;
  }
};
async function fetchPerformanceData() {
  try {
    const scenario_id = sessionStorage.getItem('scenario_id');
    const response = await axios.post(`${baseURL}/scenario/fetch_filtered_performance_data/`, {
      scenario_id: scenario_id,
      concept: sessionStorage.getItem('concept'),
      territory: sessionStorage.getItem('territory_name')
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    tableData.value = response.data;
    selectedStores.value = tableData.value?.[0]?.LOC_CD
    totalRows.value = tableData.value.length;
    console.log(response.data.length, 'testings')
    if (response.data.length === 0) {
      runPerformanceApi();
    }
  } catch (e) {
    console.error('Failed to fetch performance data', e);
  }
}

onMounted(() => {
  if(sessionStorage.getItem('runoptimiser') === 'true') {
    runPerformanceApi()
    sessionStorage.setItem('runoptimiser', 'false')
  }
  else{
      fetchPerformanceData()
  }
});

const getActionClass = (action) => {
  if (!action) return 'bg-gray-100 text-gray-600'

  const normalized = action.toLowerCase()
  if (normalized.includes('increase')) {
    return 'bg-green-100 text-green-700'
  } else if (normalized.includes('decrease')) {
    return 'bg-red-100 text-red-700'
  }
  return 'bg-gray-100 text-gray-600'
}

const toggleAction = async (row, value) => {
  row.userAction = value
  // Prepare payload
  const payload = {
    group_name: row.GRP_NM,
    department_name: row.DPT_NM,
    class_name: row.CLSS_NM,
    subclass_name: row.SUB_CLSS_NM,
    location_code: row.LOC_CD,
    new_action: value,
    scenario_id: sessionStorage.getItem('scenario_id'),
    concept: sessionStorage.getItem('concept')
  }

  try {
    const response = await axios.post(
      `${baseURL}/scenario/update_performance_action/`,
      payload
    )
  } catch (error) {
    console.error('Error updating action:', error.response?.data || error.message)
  }
}

function goToPage(page: number) {
  if (page < 1 || page > Math.ceil(totalRows.value / rowsPerPage)) return;
  currentPage.value = page;
}

function handleApplyWeights(weights) {
  updateWeights(weights)
  sessionStorage.setItem('weights', JSON.stringify(weights));
  weightsConfigure.value = false;
  runPerformanceApi()
}
const uniqueStores = computed(() => {
  const stores = tableData.value.map(item => ({
    value: item.LOC_CD,
    label: `${item.LOC_CD} - ${item.loc_nm}`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})
const uniqueSubClasses = computed(() => {
  const stores = tableData.value.map(item => ({
    value: item.SUB_CLSS_NM,
    label: item.SUB_CLSS_NM
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})
function clearAllFilters() {
  selectedStores.value = '';
  selectedSubClasses.value = [];
}
const filteredData = computed(() => {
  return tableData.value.filter(row => {
    const matchStore = selectedStores.value.length === 0 || selectedStores.value.includes(row.LOC_CD)
    const matchSubClass = selectedSubClasses.value.length === 0 || selectedSubClasses.value.includes(row.SUB_CLSS_NM)
    return matchStore && matchSubClass
  })
})
function handleFilter() {
  showFiltered.value = true;
  totalRows.value = filteredData.value.length;
  currentPage.value = 1;
}

const capitalizeWords = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
}

const actionColor = (action, newAction, actionP) => {
  if (!action) return 'bg-gray-100 text-gray-600'

  const normalized = action.toLowerCase()

  // If new_action and action_p are different → highlight
  if (newAction && actionP && newAction !== actionP) {
    if (normalized.includes('increase')) {
      return 'bg-green-200 text-green-900'
    } else if (normalized.includes('decrease')) {
      return 'bg-red-200 text-red-900'
    } else {
      return 'bg-yellow-100 text-yellow-800'
    }
  }

  return 'bg-gray-100 text-gray-600'
}
</script>

<template>
  <div class="flex w-full">
    <div v-if="isTableLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">{{ loadingMessage }}</span>
          </div>
    </div>
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="bg-primary">
        <div class="px-10 ">
          <div class="bg-white rounded-lg shadow-sm p-2 mb-2">

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4 items-end">
              <div>
                <label class="ml-2 text-sm font-medium text-gray-700">Store <span
                    class="text-red-500 ml-1">*</span></label>
                <DynamicFilter v-model="selectedStores" :multiselect="false" :close-on-select="!multiselect" label="Store" placeholder="Stores"
                  :options="uniqueStores" :searchable="(uniqueStores?.length || 0) > 10" variant="secondary" size="sm" />
              </div>
              <div>
                <label class="ml-2 text-sm font-medium text-gray-700">Sub Classes</label>
                <DynamicFilter v-model="selectedSubClasses" :multiselect="true" label="Sub Classes"
                  placeholder="Sub Classes" :options="uniqueSubClasses" :searchable="(uniqueSubClasses?.length || 0) > 10" variant="secondary" size="sm" />
              </div>
              <div class="flex gap-2">
                <button @click="clearAllFilters()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
                  Clear All
                </button>
                <button @click="handleFilter()"
                  class="rounded-lg px-4 py-2 text-sm font-medium border bg-green-600 hover:bg-green-700 text-white focus:ring-green-500">
                  Apply Filters
                </button>
              </div>
              <div class="flex justify-end gap-2">
                <button class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg shadow transition"
                  @click="weightsConfigure = true">
                  Configure Weights
                </button>

              </div>
            </div>

            <!-- Filter Actions -->

          </div>
        </div>
      </div>
      <main class="flex-1 px-4 pb-4 sm:px-6 sm:pb-6 lg:px-8 lg:pb-8">
        <!-- Loading overlay -->
        <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Updating chart...</span>
          </div>
        </div>
        <!-- <div v-if="isTableLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">{{ loadingMessage }}</span>
          </div>
        </div> -->
        <!-- <div class="justify-end flex space-x-4 mb-4"> -->
        <!-- <div>
            <button class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow transition"
              @click="weightsConfigure = true">
              Configure Weights
            </button>
          </div> -->
        <!-- <div>
            <button
              class="px-4 py-1 bg-green-500 hover:bg-green-600 text-white text-lg font-semibold rounded-lg shadow transition"
              @click="updateWeights">
              Recalculate
            </button>
          </div> -->
        <!-- </div> -->
        <div class="mt-2 rounded-lg shadow p-4">
          <div class="overflow-x-auto">
            <div class="overflow-y-auto relative max-h-[70vh]">
              <table class="w-full text-sm text-left border border-[#60A5FA] bg-white" style="--store-col-width: 8rem;">
                <thead class="bg-primary">
                  <tr>
                    <th class="px-3 py-2 border sticky top-0 left-0 z-40 bg-white w-[8rem] min-w-[8rem] text-center">Store</th>
                    <th class="px-3 py-2 border sticky top-0 z-40 bg-white w-[12rem] min-w-[12rem] text-center" style="left: var(--store-col-width);">Subclass</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Linear meter</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Units per Invoice</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Customer Penetration</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white">Cover</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Margin</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white">Productivity</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white text-nowrap">Average Selling Price</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white">Performance</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Performance Grade</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">LM Grade</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Cover Grade</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Final Grade</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white whitespace-nowrap">Recommended Action</th>
                    <th class="px-3 py-2 border sticky top-0 z-30 bg-white text-center">Chosen Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, index) in paginatedData" :key="index">
                    <td class="px-3 py-2 border text-center sticky left-0 z-30 bg-white w-[6rem] min-w-[6rem]">{{ row.LOC_CD }}</td>
                    <td class="px-3 py-2 border text-center whitespace-nowrap sticky z-30 bg-white w-[10rem] min-w-[10rem]" style="left: var(--store-col-width);">{{ capitalizeWords(row.SUB_CLSS_NM) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.Linear_meter?.toFixed(2) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.d_units?.toFixed(2) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.CUST_PEN?.toFixed(2) }}%</td>
                    <td class="px-3 py-2 border text-center">{{ Math.round(row.COVER) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.MARGIN_PERC?.toFixed(2) }}%</td>
                    <td class="px-3 py-2 border text-center">{{ row.Productivity?.toFixed(2) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.ASP?.toFixed(2) }}</td>
                    <td class="px-3 py-2 border text-center">{{ row.Performance }}</td>
                    <td class="px-3 py-2 border text-center">{{ mapCategory(row.perf_bucket_p) }}</td>
                    <td class="px-3 py-2 border text-center">{{ mapCategory(row.lm_bucket_p) }}</td>
                    <td class="px-3 py-2 border text-center">{{ mapCategory(row.cover_bucket_p) }}</td>
                    <td class="px-3 py-2 border text-center relative">
  <div class="inline-block group relative">
    <span class="font-semibold">
      {{ row.perf_bucket_p }}{{ row.lm_bucket_p }}{{ row.cover_bucket_p }}
    </span>

    <!-- Tooltip: position based on row index -->
    <div
      :class="[
        'absolute left-1/2 transform -translate-x-1/2 w-56 rounded-md bg-gray-800 text-white text-xs p-2 shadow-lg z-50 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto',
        index === 0 ? 'top-full mt-2' : 'bottom-full mb-2',
        'opacity-0 group-hover:opacity-100'
      ]"
    >
      <div><strong>Performance Grade:</strong> {{ mapCategory(row.perf_bucket_p) }}</div>
      <div><strong>LM Grade:</strong> {{ mapCategory(row.lm_bucket_p) }}</div>
      <div><strong>Cover Grade:</strong> {{ mapCategory(row.cover_bucket_p) }}</div>
    </div>
  </div>
</td>



                    <!-- Recommendation column -->
                    <td class="px-3 py-2 border text-center">
                      <span
                        :class="`inline-block capitalize px-2 py-1 rounded-full font-semibold text-sm ${getActionClass(row.action_p)}`">
                        {{ row.action_p }}
                      </span>
                    </td>
                    <td class="px-3 py-2 border text-center">
                      <select v-model="row.new_action" @change="toggleAction(row, row.new_action)"
                        class="border font-semibold rounded px-2 py-1 text-sm capitalize"
                        :class="actionColor(row.new_action, row.new_action, row.action_p)">
                        <option v-for="option in actionOptions" :key="option" :value="option">
                          {{ option }}
                        </option>
                      </select>
                    </td>
                  </tr>
                  <!-- ...existing code... -->
                </tbody>
              </table>

            </div>
          </div>
          <!-- Pagination Controls moved outside table -->
          <div class="flex justify-center items-center mt-4 gap-4">
            <button
              class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
              :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">Previous</button>
            <span>Page {{ currentPage }} of {{ Math.max(1, Math.ceil(totalRows / rowsPerPage)) }} (total {{ totalRows }}
              rows)</span>
            <button
              class="bg-green-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
              :disabled="currentPage === Math.ceil(totalRows / rowsPerPage) || totalRows === 0"
              @click="goToPage(currentPage + 1)">Next</button>
          </div>
        </div>
      </main>
    </div>
  </div>
  <transition name="fade">
    <div v-if="weightsConfigure" class="fixed inset-0 pr-10 flex items-center justify-end bg-black bg-opacity-50 z-50">
      <WeightsCalcu @close="weightsConfigure = false" @apply="handleApplyWeights" />
    </div>
  </transition>
</template>



<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.run-button {
  padding: 10px 0;
  background-color: #16A34A;
  /* Tailwind's green-600 */
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease, color 0.2s ease;
  width: 80px;
}

.run-button:hover {
  background-color: #15803D;
  /* Tailwind's green-700 */
}

/* Custom loading animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>