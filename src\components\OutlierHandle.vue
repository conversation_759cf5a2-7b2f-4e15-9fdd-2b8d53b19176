<template>
  <div>
    <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
        <span class="text-gray-700">Loading...</span>
      </div>
    </div>
    <div v-else>
      <!-- Filters -->
      <div class="p-6 space-y-6 bg-white shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Cluster<span
                class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="filters.cluster" :multiselect="false" label="Cluster" placeholder="Select Clusters"
              :options="clusterOptions || []" value-key="value" label-key="label" :searchable="(clusterOptions?.length || 0) > 10" variant="outline" @change="handelClusterChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Store<span class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="filters.storeId" :multiselect="true" label="Store" placeholder="Select Stores"
              :options="storeOptions || []" value-key="code" label-key="name" :searchable="(storeOptions?.length || 0) > 10" variant="outline" @change="handelStoreChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Groups</label>
            <DynamicFilter v-model="filters.group" :multiselect="true" label="Groups" placeholder="Select Group"
              :options="groupOptions || []" value-key="code" label-key="name" :searchable="(groupOptions?.length || 0) > 10" variant="outline" @change="handelGroupChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Departments</label>
            <DynamicFilter v-model="filters.department" :multiselect="true" label="Department"
              placeholder="Select Departments" :options="departmentOptions || []" value-key="code" label-key="name" @change="handelDepartmentChange()"
              :searchable="(departmentOptions?.length || 0) > 10" variant="outline" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Classes</label>
            <DynamicFilter v-model="filters.class" :multiselect="true" label="Class" placeholder="Select Classes"
              :options="classOptions || []" value-key="code" label-key="name" :searchable="(classOptions?.length || 0) > 10" variant="outline"
              size="sm" />
          </div>

          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Sub Classes</label>
            <DynamicFilter v-model="filters.subclass" :multiselect="true" label="SubClass"
              placeholder="Select Sub classes" :options="subclassOptions || []" value-key="code" label-key="name"
              :searchable="(subclassOptions?.length || 0) > 10" variant="outline" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700 flex items-center mb-1">
              Outlier Status
              <div class="relative inline-block ml-1">
                <!-- Info button -->
                <button type="button" class="text-gray-500 italic focus:outline-none"
                  @mouseenter="showOutlierInfo = true" @mouseleave="showOutlierInfo = false"
                  @focus="showOutlierInfo = true" @blur="showOutlierInfo = false" tabindex="0">
                  i
                </button>
                <!-- Tooltip -->
                <div v-if="showOutlierInfo"
                  class="absolute left-1/2 -translate-x-1/2 mt-2 w-64 rounded-lg bg-gray-600 text-white text-sm p-3 shadow-lg z-50 pointer-events-none"
                  style="pointer-events: none;">
                  <p class="font-semibold">Major Outlier</p>
                  <p class="mb-2">LM contribution spike without matching sales (GMV/day). Likely an anomaly.</p>
                  <p class="font-semibold">Minor Outlier</p>
                  <p>Unusual LM contribution compared to similar stores/months. May not need action.</p>
                </div>
              </div>
            </label>
            <DynamicFilter v-model="filters.outlierStatus" :multiselect="false" label="Outlier"
              placeholder="Select Outlier Status" :options="outlierStatusOptions || []" value-key="code"
              label-key="name" :searchable="false" variant="outline" size="sm" />
          </div>
          <div class="flex mt-6 gap-2">
            <button @click="resetFilters"
              class="px-4 py-2 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
              Clear All
            </button>
            <button @click="applyFilters"
              class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-green-600 hover:bg-green-700 text-white focus:ring-green-500">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
      <!-- Table -->
      <!-- Data Table -->
      <div class="overflow-x-auto px-6">
        <div class="flex justify-between items-center mt-2">
          <!-- Left Side: Visualization and Download -->
          <div class="flex gap-3">
            <button @click="exportData()"
              class="flex items-center gap-2 px-4 py-2 h-10 bg-green-600 hover:bg-green-700 text-white text-sm font-semibold rounded-lg shadow transition">
              <Download class="h-4 w-4" />
              Download
            </button>

            <button @click="openChart()"
              class="flex items-center gap-2 px-4 py-2 h-10 bg-green-600 hover:bg-green-700 text-sm text-white font-semibold rounded-lg shadow transition">
              <BarChart3 class="h-4 w-4" />
              Visualization
            </button>
          </div>

          <!-- Right Side: Approve and Reject -->
          <div class="flex gap-3">
            <button :disabled="isApprove || isReject" @click="approveAll()"
              class="flex items-center gap-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
              <div v-if="isApprove" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <Check class="h-6 w-4" />
              <span>Approve All</span>
            </button>

            <button :disabled="isApprove || isReject" @click="rejectAll()"
              class="flex items-center gap-2 bg-red-500 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
              <div v-if="isReject" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <X class="h-6 w-4" />
              <span>Reject All</span>
            </button>
          </div>
        </div>

        <!-- No Data Message -->
        <div v-if="paginatedRows.length === 0" class="text-center py-8 text-gray-500">
          No outlier data found for the selected filters
        </div>

        <!-- Table Container with Sticky Columns -->
        <div v-else class="relative">
          <!-- Table Wrapper for Horizontal Scroll -->
          <div class="overflow-x-auto max-h-[70vh] overflow-y-auto" style="scroll-behavior: smooth;">
<table class="table-auto border-separate border-spacing-0 border-1 border border-gray-300 rounded-md mb-4 mt-3 w-full bg-white">
  <!-- Sticky Table Header -->
  <thead class="bg-gray-100 text-sm tracking-wide sticky top-0 z-20">
    <tr>
      <!-- Frozen Columns -->
      <th class="px-3 py-3 border-r border-l-0 border-gray-300 bg-gray-100 text-center sticky left-0 z-30 w-[80px]">Cluster</th>
      <th class="px-3 py-3 border-r border-l-0 border-gray-300 bg-gray-100 text-center sticky left-[80px] z-30 w-[72px]">Store</th>
      <th class="px-3 py-3 border-r border-gray-300 bg-gray-100 text-center sticky left-[152px] z-30 w-[96px]">Group</th>
      <th class="px-3 py-3 border-r border-gray-300 bg-gray-100 text-center sticky left-[248px] z-30 w-[112px]">Department</th>
      <th class="px-3 py-3 border-r border-gray-300 bg-gray-100 text-center sticky left-[360px] z-30 w-[200px]">Class</th>
      <th class="px-3 py-3 border-r border-gray-300 bg-gray-100 text-center sticky left-[500px] z-30 w-[180px]">Subclass</th>

      <!-- Scrollable Columns -->
      <th class="px-4 py-3 border border-l-0  border-gray-300 text-center min-w-[80px]">Month</th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[80px]">LM</th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[120px] whitespace-nowrap">LM Contribution</th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[100px]">
        {{ performanceMetric === 'REVENUE' ? 'Revenue/day' : 'GMV/day' }}
      </th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[120px]">Outlier Status</th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[100px]">Revised LM</th>
      <th class="px-4 py-3 border border-gray-300 text-center min-w-[120px]">Action</th>
    </tr>
  </thead>

  <!-- Table Body -->
  <tbody>
    <tr v-for="(row, index) in paginatedRows" :key="index"
      :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'" class="text-sm">

      <!-- Frozen Columns -->
      <td class="px-3 py-2 border-r border-l-0 border-gray-300 whitespace-nowrap bg-white text-center sticky left-0 z-10 w-[80px]">{{ row.cluster }}</td>
      <td class="px-3 py-2 border-r border-l-0  border-gray-300 whitespace-nowrap bg-white text-center sticky left-[80px] z-10 w-[72px]">{{ row.storeId }}</td>
      <td class="px-3 py-2 border-r border-gray-300 whitespace-nowrap bg-white text-center sticky left-[152px] z-10 w-[96px]">{{ row.group }}</td>
      <td class="px-3 py-2 border-r border-gray-300 whitespace-nowrap bg-white text-center sticky left-[248px] z-10 w-[112px]">{{ row.department }}</td>
      <td class="px-3 py-2 border-r border-gray-300 whitespace-nowrap bg-white text-center sticky left-[360px] z-10 w-[200px]">{{ row.class }}</td>
      <td class="px-3 py-2 border-r border-gray-300 whitespace-nowrap bg-white text-center sticky left-[500px] z-10 w-[200px]">{{ row.subclass }}</td>

      <!-- Scrollable Columns -->
      <td class="px-4 py-2 border border-l-0 border-gray-300 text-center min-w-[80px] whitespace-nowrap">{{ formatMonth(row.month) }}</td>
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[80px]">
        {{ row.totalLm !== null && row.totalLm !== undefined ? row.totalLm.toFixed(2) : '' }}
      </td>
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[120px]">
        {{ row.lmContrib }}
      </td>
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[100px]">
        {{ row.perDay !== null && row.perDay !== undefined ? row.perDay.toFixed(2) : '' }}
      </td>

      <!-- Outlier Status -->
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[120px]">
        <span :class="getOutlierStatusClass(row.outlierStatus)">
          {{ getLabelByValue(row.outlierStatus) }}
        </span>
      </td>

      <!-- Revised LM -->
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[100px]">
        {{ row.suggestedTotalLm !== null && row.suggestedTotalLm !== undefined ?
          row.suggestedTotalLm.toFixed(2) : '' }}
      </td>

      <!-- Action Buttons -->
      <td class="px-4 py-2 border border-gray-300 text-center min-w-[120px]">
        <div v-if="row.outlierFinalStatus === 'APPROVED'">
          <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-semibold">Approved</span>
        </div>
        <div v-else-if="row.outlierFinalStatus === 'REJECTED'">
          <span class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs font-semibold">Rejected</span>
        </div>
        <div v-else-if="isOutlier(row.outlierStatus)" class="flex gap-1 justify-center">
          <button v-if="!row.processingReject" @click="approveRow(row)"
            :disabled="row.processingApprove || row.processingReject"
            class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-2 py-1 rounded text-xs transition-colors">
            {{ row.processingApprove ? "Processing..." : "Approve" }}
          </button>
          <button v-if="!row.processingApprove" @click="rejectRow(row)"
            :disabled="row.processingApprove || row.processingReject"
            class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-2 py-1 rounded text-xs transition-colors">
            {{ row.processingReject ? "Processing..." : "Reject" }}
          </button>
        </div>
        <div v-else>-</div>
      </td>
    </tr>
  </tbody>
</table>

          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-between items-center mt-6 px-6">
        <div class="text-sm text-gray-600">
          Page {{ currentPage }} of {{ totalPages }} ({{ totalCount }} total
          rows)
        </div>
        <div class="flex gap-2 mb-2">
          <button @click="previousPage" :disabled="currentPage === 1"
            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors">
            Previous
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="bg-green-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors">
            Next
          </button>
        </div>
      </div>

      <!-- Chart Modal -->
      <div v-if="showChartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg px-6 py-1 max-w-4xl w-full mx-4 max-h-[99vh]" @click.stop>
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Outlier Chart</h2>
            <button @click="closeChart" class="text-gray-500 hover:text-gray-700 text-2xl">
              &times;
            </button>
          </div>
          <OutlierChart :originalRows="dataDumpForFiltering" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import Multiselect from "@vueform/multiselect"; // corrected import
import "@vueform/multiselect/themes/default.css"; // required for styling
import OutlierChart from "./OutlierChart.vue";
import DynamicFilter from "./common/DynamicFilter.vue";
import axios from "axios";
import { useStepsStore } from "../stores/NavigationStore";
import { Download, BarChart3, Check, X } from "lucide-vue-next";
import { ref as vueRef } from 'vue'
const showOutlierInfo = vueRef(false)
const stepsStore = useStepsStore();

// Filters and options
const filters = ref({
  group: [],
  department: [],
  class: [],
  subclass: [],
  storeId: [],
  outlierStatus: '',
  cluster: 0,
});

const clusterOptions = computed(() => {
  const uniqueClusters = [...new Set(filtersData.value.map(r => r.cluster_num))]
  return uniqueClusters.map(cluster => ({
    value: cluster,
    label: `${cluster}`
  }))
})

const storeOptions = computed(() => {
  // Accept 0 as a valid cluster value
  if (filters.value.cluster === null || filters.value.cluster === undefined) return []
  const filtered = filtersData.value.filter(
    store => store.cluster_num === filters.value.cluster
  )
  const uniqueStores = [
    ...new Map(filtered.map(store => [store.loc_cd, store])).values()
  ]
  return uniqueStores.map(store => ({
    value: store.loc_cd,
    label: `${store.loc_cd} - ${store.loc_nm}`
  }))
})


// Hierarchical filter options
const groupOptions = computed(() => {
  if (!filters.value.storeId || filters.value.storeId.length === 0) return []
  const filtered = filtersData.value.filter(store =>
    filters.value.storeId.includes(store.loc_cd)
  )
  const uniqueGroups = [...new Set(filtered.map(store => store.grp_nm))]

  return uniqueGroups.map(g => ({
    value: g,
    label: g
  }))
})


const departmentOptions = computed(() => {
  let filtered = filtersData.value
  // If storeId is selected, filter by storeId
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd))
  }
  // If group is selected, filter by group
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm))
  }
  // Get unique departments
  const uniqueDepartments = [...new Set(filtered.map(store => store.dpt_nm))]
  return uniqueDepartments.map(d => ({ value: d, label: d }))
})

const classOptions = computed(() => {
  let filtered = filtersData.value
  // If storeId is selected, filter by storeId
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd))
  }
  // If group is selected, filter by group
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm))
  }
  // If department is selected, filter by department
  if (filters.value.department && filters.value.department.length > 0) {
    filtered = filtered.filter(store => filters.value.department.includes(store.dpt_nm))
  }
  // Get unique classes
  const uniqueClasses = [...new Set(filtered.map(store => store.clss_nm))]
  return uniqueClasses.map(c => ({ value: c, label: c }))
})

const subclassOptions = computed(() => {
  let filtered = filtersData.value;
  // If storeId is selected, filter by storeId
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd));
  }
  // If group is selected, filter by group
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm));
  }
  // If department is selected, filter by department
  if (filters.value.department && filters.value.department.length > 0) {
    filtered = filtered.filter(store => filters.value.department.includes(store.dpt_nm));
  }
  // If class is selected, filter by class
  if (filters.value.class && filters.value.class.length > 0) {
    filtered = filtered.filter(store => filters.value.class.includes(store.clss_nm));
  }
  // Get unique subclasses
  const uniqueSubclasses = [...new Set(filtered.map(store => store.sub_clss_nm))];
  return uniqueSubclasses.map(s => ({ value: s, label: s }));
});


const outlierStatusOptions = [
  { value: "MAJOR_OUTLIER", label: "Major Outlier" },
  { value: "MINOR_OUTLIER", label: "Minor Outlier" },
  { value: "NON_OUTLIER", label: "Non Outlier" },
];

// Data
const originalRows = ref([]); // Keep original data separate
const loading = ref(false);
const isApprove = ref(false);
const isReject = ref(false);
const error = ref(null);
// Pagination
const currentPage = ref(1);
const pageSize = 10;
const totalCount = ref(0);

// Chart Modal
const showChartModal = ref(false);
const selectedRow = ref(null);
const performanceMetric = ref(sessionStorage.getItem("performance_metric"));
// Computed properties for pagination
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize));
const paginatedRows = computed(() => originalRows.value);

const formatMonth = (yyyymm) => {
  const year = yyyymm.slice(0, 4);
  const month = yyyymm.slice(4);
  const date = new Date(`${year}-${month}-01`);
  return date.toLocaleString('default', { month: 'short', year: 'numeric' }); // e.g., "Aug 2024"
};

const parseFormattedMonth = (formatted) => {
  const date = new Date(formatted);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}${month}`;
};


async function exportData() {
  const storeList = filters.value.storeId?.length > 0
    ? filters.value.storeId
    : (JSON.parse(sessionStorage.getItem('loc_codes') || '[]'));

  if (!storeList || storeList.length === 0) {
    return;
  }

  try {
    const params = {
      concept: sessionStorage.getItem("concept"),
      scenario_id: sessionStorage.getItem("scenario_id"),
      territory: sessionStorage.getItem("territory_name"),
      performance_metric: performanceMetric.value,
      loc_cd: storeList,
    };

    // Await axios call and get JSON data from response.data
    const response = await axios.get("scenario/downloadOutlierData/", { params });

    // Extract data from response
    const data = response.data.data;

    if (!data || data.length === 0) {
      return;
    }

    // Use the fetched data to build CSV
    const performanceLabel = `${performanceMetric.value === 'REVENUE' ? 'Revenue' : 'GMV'}/day`;
    const columns = [
    { label: "Cluster", key: "cluster_num" },
    { label: "Store", key: "loc_nm" },
    { label: "Group", key: "grp_nm" },
    { label: "Department", key: "dpt_nm" },
    { label: "Class", key: "clss_nm" },
    { label: "Subclass", key: "sub_clss_nm" },
    { label: "Month", key: "month" },
    { label: "LM", key: "total_lm" },
    { label: "LM Contribution", key: "lm_contribution_in_store" },
    { label: performanceLabel, key: "per_day" },
    { label: "Outlier Status", key: "outlier_status" },
    { label: "Revised LM", key: "suggested_total_lm" },
    { label: "Final Status", key: "outlier_status_final" },
  ];


    // CSV header
    const header = columns.map(col => col.label).join(",");

    // CSV rows
    const dataRows = data.map(row => {
      return columns.map(col => {
        let val = row[col.key];

        if (col.key === "month" && typeof val === "string" && /^\d{6}$/.test(val)) {
          const year = val.slice(0, 4);
          const monthNum = parseInt(val.slice(4, 6), 10);
          const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
          const monthName = monthNames[monthNum - 1];
          val = `="${monthName} ${year}"`;
        }

        if (typeof val === "number") val = val.toFixed(2);
        if (typeof val === "string" && (val.includes(",") || val.includes('"'))) {
          val = `"${val.replace(/"/g, '""')}"`;
        }
        return val ?? "";
      }).join(",");
    });

    // Full CSV content
    const csvContent = [header, ...dataRows].join("\r\n");

    // Download CSV
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `filtered_outlier_data_${new Date().toISOString().slice(0,10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

  } catch (err) {
    console.error("Error downloading CSV:", err);
  }
}


let filtersData = ref([]);
let storeDataAvailable = ref([]);
let dataDumpForFiltering = ref([]);
const fetchOutlierData = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get("scenario/outlier/", {
      params: {
        concept: sessionStorage.getItem('concept'),
        scenario_id: sessionStorage.getItem('scenario_id'),
        territory: sessionStorage.getItem('territory_name'),
        performance_metric: performanceMetric.value,
        loc_cd: (filters.value.storeId && filters.value.storeId.length > 0)
          ? filters.value.storeId
          : (JSON.parse(sessionStorage.getItem('loc_codes') || '[]'))[0] || null,
        cluster: filters.value.cluster,
        group: filters.value.group,
        department: filters.value.department, 
        class_field: filters.value.class,
        sub_class: filters.value.subclass,
        outlier_status: filters.value.outlierStatus,
        page: currentPage.value,
        limit: pageSize
      },
    });
    filtersData.value = response.data.filters || [];
    storeDataAvailable.value = response.data.selected_loc_cd
    


    const mappedData = response.data.data.map((item) => ({
      group: item.grp_nm,
      department: item.dpt_nm,
      class: item.clss_nm,
      subclass: item.sub_clss_nm,
      storeId: item.loc_cd,
      storeName: item.loc_nm,
      month: item.month,
      totalLm: item.total_lm,
      lmContrib: `${item.lm_contribution_in_store}%`,
      perDay: item.per_day,
      outlierStatus: item.outlier_status || "NON_OUTLIER",
      outlierFinalStatus: item.outlier_status_final,
      suggestedTotalLm: item.suggested_total_lm,
      action: "APPROVE", // Default action
      processing: false, // For individual row loading states
      cluster: item.cluster_num,
      new_cluster: item.new_cluster_num,
    }));
    // Store original data
    dataDumpForFiltering.value = mappedData;
    originalRows.value = mappedData;
    totalCount.value = response.data.count || 0;
  } catch (err) {
    console.error("Error fetching outlier data:", err);
    error.value =
      err.response?.data?.message ||
      err.message ||
      "Failed to load outlier data";
  } finally {
    loading.value = false;
  }
};

const isOutlier = (status) => {
  return status === "MAJOR_OUTLIER";
  return ["MAJOR_OUTLIER"].includes(status);
}

const getOutlierStatusClass = (status) => {
  switch (status) {
    case "NON_OUTLIER":
      return "bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-semibold whitespace-nowrap";
    case "MINOR_OUTLIER":
      return "bg-neutral text-white px-3 py-1 rounded text-xs whitespace-nowrap";
    case "MAJOR_OUTLIER":
      return "bg-negative text-white px-3 py-1 rounded text-xs whitespace-nowrap";
    default:
      return "bg-gray-500 text-white px-2 py-1 rounded text-xs";
  }
};

// Filter functions
const applyFilters = () => {
  currentPage.value = 1;

  fetchOutlierData();
};

const handelClusterChange = () => {
  filters.value.storeId = [];
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
}
const handelStoreChange = () => {
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
}

const handelGroupChange = () => {
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
};

const handelDepartmentChange = () => {
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
};
const resetFilters = () => {
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  // filters.value.storeId = [];
  filters.value.outlierStatus = '';
  // filters.value.cluster = 0;
};

const getLabelByValue = (value) => {
  const option = outlierStatusOptions.find((opt) => opt.value === value);
  return option ? option.label : null;
};

// Pagination functions
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchOutlierData();
  }
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchOutlierData();
  }
};

// API functions for approve/reject
const updateOutlierStatus = async (rowsToUpdate, finalStatus) => {
  try {
    const rowsArray = Array.isArray(rowsToUpdate)
      ? rowsToUpdate
      : [rowsToUpdate];

    const perfMetric = sessionStorage.getItem("performance_metric");

    const payload = rowsArray.map((row) => {
      const base = {
        sub_clss_nm: row.subclass,
        loc_cd: row.storeId,
        outlier_status_final: finalStatus,
        // month: parseFormattedMonth(row.month),
        month: row.month,
        concept: sessionStorage.getItem("concept"),
        scenario_id: sessionStorage.getItem("scenario_id"),
      };

      if (perfMetric === "GMV") {
        base.gmv_suggested_total_lm = row.suggestedTotalLm;
      } else if (perfMetric === "REVENUE") {
        base.rev_suggested_total_lm = row.suggestedTotalLm;
      }

      return base;
    });

    const response = await axios.patch("scenario/outlier/", payload);

    if (response.status === 200) {
      // Update the row status locally
      rowsArray.forEach((row) => {
        row.outlierFinalStatus = finalStatus;
      });
      return true;
    }
  } catch (err) {
    console.error("Error updating outlier status:", err);
    error.value =
      err.response?.data?.message || err.message || "Failed to update status";
    return false;
  }
};

const getAllOutliers = () => {
  return originalRows.value.filter((row) => isOutlier(row.outlierStatus));
};

const approveRow = async (row) => {
  sessionStorage.setItem('runoptimiser', 'true')
  row.processing = true;
  row.processingApprove = true;
  row.processingReject = false;
  const success = await updateOutlierStatus([row], "APPROVED");
  if (success) {
    row.outlierFinalStatus = "APPROVED";
    // Also update the original data to maintain consistency
    const originalRow = originalRows.value.find(
      (r) =>
        r.subclass === row.subclass &&
        r.storeId === row.storeId &&
        r.month === row.month
    );
    if (originalRow) {
      originalRow.outlierFinalStatus = "APPROVED";
    }
  }
  row.processing = false;
  row.processingApprove = false;
};

const rejectRow = async (row) => {
  sessionStorage.setItem('runoptimiser', 'true')
  row.processing = true;
  row.processingReject = true;
  row.processingApprove = false;
  const success = await updateOutlierStatus([row], "REJECTED");
  if (success) {
    row.outlierFinalStatus = "REJECTED";
    // Also update the original data to maintain consistency
    const originalRow = originalRows.value.find(
      (r) =>
        r.subclass === row.subclass &&
        r.storeId === row.storeId &&
        r.month === row.month
    );
    if (originalRow) {
      originalRow.outlierFinalStatus = "REJECTED";
    }
  }
  row.processing = false;
  row.processingReject = false;
};

// Bulk approve all outliers in current filtered view
const approveAll = async () => {
  sessionStorage.setItem('runoptimiser', 'true')
  isApprove.value = true
  const outlierRows = originalRows.value.filter(
    (row) => isOutlier(row.outlierStatus) && isOutlier(row.outlierFinalStatus)
  );
  if (outlierRows.length === 0) return;
  // Set processing state
  outlierRows.forEach((row) => (row.processing = true));
  const success = await updateOutlierStatus(outlierRows, "APPROVED");
  if (success) {
    outlierRows.forEach((row) => {
      row.outlierFinalStatus = "APPROVED";
      // Also update the original data to maintain consistency
      const originalRow = originalRows.value.find(
        (r) =>
          r.subclass === row.subclass &&
          r.storeId === row.storeId &&
          r.month === row.month
      );
      if (originalRow) {
        originalRow.outlierFinalStatus = "APPROVED";
      }
    });
  }
  outlierRows.forEach((row) => (row.processing = false));
  isApprove.value = false
};

// Bulk reject all outliers in current filtered view
const rejectAll = async () => {
  sessionStorage.setItem('runoptimiser', 'true')
  isReject.value = true
  const outlierRows = originalRows.value.filter(
    (row) => isOutlier(row.outlierStatus) && isOutlier(row.outlierFinalStatus)
  );
  if (outlierRows.length === 0) return;
  // Set processing state
  outlierRows.forEach((row) => (row.processing = true));
  const success = await updateOutlierStatus(outlierRows, "REJECTED");
  if (success) {
    outlierRows.forEach((row) => {
      row.outlierFinalStatus = "REJECTED";
      // Also update the original data to maintain consistency
      const originalRow = originalRows.value.find(
        (r) =>
          r.subclass === row.subclass &&
          r.storeId === row.storeId &&
          r.month === row.month
      );
      if (originalRow) {
        originalRow.outlierFinalStatus = "REJECTED";
      }
    });
  }
  outlierRows.forEach((row) => (row.processing = false));
  isReject.value = false
};

// Chart Modal Functions
const openChart = () => {
  showChartModal.value = true;
};

const closeChart = () => {
  showChartModal.value = false;
};

onMounted(() => {
  fetchOutlierData();
});
</script>

<style scoped>
.sticky {
  box-shadow: 2px 0 0 0 #d1d5db; /* gray-300 */
}

</style>