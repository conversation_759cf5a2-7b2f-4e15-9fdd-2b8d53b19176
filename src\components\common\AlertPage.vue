<script setup>
import { defineProps, defineEmits } from "vue"

const props = defineProps({
    message: {
        type: String,
        required: true
    },
    visible: {
        type: Boolean,
        default: false
    },
    header: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(["ok", "cancel"])

const emitOk = () => emit("ok")
const emitCancel = () => emit("cancel")
</script>
<template>
    <div v-if="visible" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
        <div class="bg-white rounded-lg shadow-lg w-120 p-6 text-center">
            <div v-if="header" class="text-xl font-semibold mb-4">{{ header }}</div>
            <!-- Dynamic message -->
            <p class="text-gray-800 mb-6 whitespace-pre-line">{{ message }}</p>

            <!-- Buttons -->
            <div class="flex justify-center space-x-4">
                <button @click="emitCancel" class="px-4 py-2 rounded bg-gray-300 text-gray-700 hover:bg-gray-400">
                    Cancel
                </button>
                <button @click="emitOk" class="px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700">
                    Submit
                </button>
            </div>
        </div>
    </div>
</template>
