import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_BASE_URL;


export const fetchStores = async (concept, territory) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/scenario/get-store/`, {
      params: {
        concept: concept,
        territory: territory
      }
    })
    console.log("response value ", response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch store list:', error)
    return []
  }
}

export const saveScenarioAPI = async (data) => {
  try {

    const response = await axios.post(`${API_BASE_URL}/scenario/create-scenario/`, 
      data,
       {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
    sessionStorage.setItem('scenario_id',response.data.scenario_details.scenario_id)
    return response.data;
  } catch (error) {
    throw error.response;
  }
};


export const getClustersAPI = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/clusters/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}

export const updateStoreClustersAPI = async (payload) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/scenario/clusters/`, payload);
    return response.data;
  } catch (error) {
    throw error.response || error;
  }
};

export const deleteStoreFromClusterAPI = async ({ loc_cd, cluster_num, concept }) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/scenario/clusters/`, {
      data: { loc_cd, cluster_num, concept }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const getPreOptAPI = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/preopt/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}

export const fetchRefPeriod = async (concept, territory) => {
  try {
    console.log("concept 1111111 ", concept, territory)
    const response = await axios.get(`${API_BASE_URL}/scenario/getRefPeriod/`, {
      params: {
        concept: concept,
        territory: territory
      }
    })
    console.log("response value ", response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch store list:', error)
    return []
  }
}

export const updateScenarioStatus = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/updateScenarioStatus/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}