<template>
  <div>
  <!-- <div class="bg-white rounded-2xl shadow-sm border border-gray-200"> -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
        <span class="text-gray-700">Loading...</span>
      </div>
    </div>
  <div >
    <div class="px-4 py-6 sm:px-6 sm:py-8 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-3">
          <label class="flex items-center text-sm font-medium mb-2">
            <StickyNote class="w-4 h-4 mr-2 text-secondary" />Scenario Name<span class="text-red-500 ml-1">*</span>
          </label>
          <input type="text" v-model="formData.scenarioName" placeholder="Enter Scenario Name"
            class="w-full h-10 px-4 text-sm font-medium rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-primary" />
        </div>
        
        
        <div class="space-y-5">
          <label class="flex items-center text-sm font-semibold">
            <Bolt class="w-4 h-4 mr-2 text-secondary" />
            Territory <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.territory"
          :multiselect="false"
          label="Territories"
          placeholder="Select Territories"
          :options="territories"
           value-key="code"
           label-key="name"
          :searchable="false"
          :close-on-select="!multiselect"
          variant="outline"
          size="sm"
        />
        
        </div>
        <!-- Store Configuration -->
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Bolt class="w-4 h-4 mr-2 text-secondary" />
            Store Configuration <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.configurationStore"
          :multiselect="false"
          label="Store Configuration"
          placeholder="Select Store Configuration"
          :options="configurationStoreOptions"
          :searchable="false"
          :close-on-select="!multiselect"
          variant="outline"
          size="sm"
          @change="setStore(formData.configurationStore)"
        />
          
        </div>

        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <SunSnow class="w-4 h-4 mr-2 text-secondary" />
            Season Type <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.seasonType"
          :multiselect="false"
          label="Season Type"
          placeholder="Select Season Type"
          :options="seasonTypeOptions"
           value-key="value"
           label-key="label"
          :searchable="false"
          :close-on-select="!multiselect"
          variant="outline"
          size="sm"
          @update:modelValue="onSeasonTypeSelect"
        />
      </div> 
         
        
        <!-- Performance Metric -->
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Target class="w-4 h-4 mr-2 text-secondary" />
            Performance Metric <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.performanceMetric"
          :multiselect="false"
          label="Performance metric"
          placeholder="Select performance metric"
          :options="performanceOptions"
           value-key="value"
           label-key="label"
          :searchable="false"
          :close-on-select="!multiselect"
          variant="outline"
          size="sm"
        />
          
        </div>

        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <SunSnow class="w-4 h-4 mr-2 text-secondary" />
            Event <span class="text-red-500 ml-1">*</span>
          </label>
          <input type="text" v-model="formData.event" placeholder="Enter Event Name" :readonly="formData.seasonType === 'in season'"
            :class="[
              'w-full h-10 px-4 rounded-lg border font-medium focus:outline-none focus:ring-2',
              formData.seasonType === 'in season'
                ? 'bg-gray-100 text-gray-500 border-gray-200 cursor-not-allowed'
                : 'bg-white text-black border-gray-300 focus:ring-primary'
            ]" />
        </div>
      </div>

      <!-- Store Selection -->
      <div v-if="formData.configurationStore === 'Selected Stores'" class="mt-6 w-1/2">
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Filter class="w-4 h-4 mr-2 text-secondary" />
            Store Selection <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter
          v-model="formData.storeSelection"
          :multiselect="true"
          label="stores"
          placeholder="Select stores"
          :options="storeOptions"
          value-key="value"
          label-key="label"
          :searchable="true"
          variant="outline"
          size="sm"

        />
          
        </div>
      </div>

      <!-- Evaluation Period -->
      <div class="mt-8 text-sm">
        <h3 class="text-md font-semibold mb-4 flex items-center">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Calendar class="w-4 h-4 text-secondary" />
          </div>
          Evaluation Period <span class="text-red-500 ml-1">*</span>
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2">Start Date</label>
            <input placeholder="01-01-2025" pattern="\d{2}-\d{2}-\d{4}" type="date" v-model="formData.evaluationPeriod.startDate" @change="validateEvaluationPeriod"
              class="w-full px-4 py-3 bg-white h-10 border border-primary rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">End Date</label>
            <input type="date" v-model="formData.evaluationPeriod.endDate" @change="validateEvaluationPeriod"
              class="w-full px-4 py-3 bg-white border h-10 border-primary rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
        </div>
      </div>
      
      <!-- Reference Period -->
      <div class="mt-8 text-sm">
        <h3 class="text-md font-semibold mb-4 flex items-center">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Calendar class="w-4 h-4 text-secondary" />
          </div>
          Reference Period <span class="text-red-500 ml-1">*</span>
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2">Start Month</label>
            <input type="month" v-model="formData.referencePeriod.startDate" :min="startMonth" :max="endMonth" @change="validateReferencePeriod" placeholder="date selection"
              class="w-full px-4 py-3 bg-white h-10 text-sm border border-green-100 rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">End Month</label>
            <input type="month" v-model="formData.referencePeriod.endDate" :min="startMonth" :max="endMonth" @change="validateReferencePeriod"
              class="w-full px-4 py-3 bg-white border text-sm h-10 border-green-100 rounded-lg focus:ring-2 focus:ring-secondary" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- File Upload Section -->
  <div class="p-10">
    <div class="flex items-center mb-4">
      <div class="w-8 h-8 bg-primary rounded-xl flex items-center justify-center mr-4">
        <Upload class="w-4 h-4 text-secondary" />
      </div>
      <div>
        <h2 class="text-md font-bold">Upload Files</h2>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 px-2">
      <FileUploadArea fileKey="sqft" label="Square Feet" :icon="BarChart3" @upload-result="handleUploadResult" />
      <FileUploadArea fileKey="mdq" label="MDQ" :icon="TrendingUp" @upload-result="handleUploadResult" />
      <FileUploadArea fileKey="cover" label="Cover & Depth <span class='text-red-500 ml-1'>*</span>" :icon="FileText" @upload-result="handleUploadResult" />
      <FileUploadArea fileKey="exclusion" label="Exclusions" :icon="SquaresExclude" @upload-result="handleUploadResult" />
    </div>

    <!-- Continue Button -->
    <div class="flex justify-end mt-8">
      <button x
        @click="handleContinue(); saveOptimiserProgress()"
        :disabled="!isFormComplete || isLoading"
        :class="{
          'bg-green-600 hover:bg-green-700 text-white cursor-pointer': isFormComplete && !isLoading,
          'bg-gray-300 text-gray-500 cursor-not-allowed': !isFormComplete || isLoading
        }"
        class="px-8 py-3 rounded-lg font-semibold text-sm transition-colors duration-200 flex items-center"
      >
        <div v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        Next →
      </button>
    </div>
  </div>

  <PopupModal :show="showPopup" :message="popupMessage" @close="showPopup = false" />

</template>




<script setup>

import { onMounted, ref, reactive, provide, toRaw, watch, computed } from 'vue'
import Multiselect from 'vue-multiselect'
import DynamicFilter from './common/DynamicFilter.vue'
import Datepicker from 'vue3-datepicker'
import FileUploadArea from './FileUploadArea.vue'
import { StickyNote,Calendar, Filter, ChevronDown, Space, Target, Check, X, Upload, BarChart3, TrendingUp, FileText, SquaresExclude, Bolt, SunSnow } from 'lucide-vue-next'
import { fetchStores, saveScenarioAPI, updateScenarioStatus, fetchRefPeriod} from '../services/api'
import { useStepsStore } from '../stores/NavigationStore'
import { useScenarioStore } from '../stores/ScenarioStore'
import  PopupModal from './PopUpModal.vue'
import axios from 'axios'
import { useRoute } from 'vue-router'
const route = useRoute();
const stepsStore = useStepsStore()
const scenarioStore = useScenarioStore()

// Add loading state for continue button
const isLoading = ref(false)
const startMonth = ref('2024-08')
const endMonth = ref('2025-09')
const showPopup = ref(false)
const popupMessage = ref('')

const files = ref({
  sqft: { filename: null, fileId: null, fileObject: null, error: false },
  mdq: { filename: null, fileId: null, fileObject: null, error: false },
  cover: { filename: null, fileId: null, fileObject: null, error: false },
  exclusion: { filename: null, fileId: null, fileObject: null, error: false }
})

const dragActive = ref(null)
const loadingStores = ref(false)
const isEvaluationPeriodValid = ref(true)
const isReferencePeriodValid = ref(true)

// Computed property to check if form is complete
const isFormComplete = computed(() => {
  const requiredFields = [
    formData.scenarioName,
    formData.territory,
    formData.configurationStore,
    formData.seasonType,
    formData.performanceMetric,
    formData.event,
    formData.evaluationPeriod.startDate,
    formData.evaluationPeriod.endDate,
    formData.referencePeriod.startDate,
    formData.referencePeriod.endDate,
    // files.value.sqft.filename || files.value.sqft.fileObject,
    files.value.cover.filename || files.value.cover.fileObject // require Cover file
  ]

  if (!isEvaluationPeriodValid.value || !isReferencePeriodValid.value) {
    return false
  }

  // Check if Selected Stores configuration requires store selection
  if (formData.configurationStore === 'Selected Stores' && (!formData.storeSelection || formData.storeSelection.length === 0)) {
    return false
  }

  const hasFileErrors = Object.values(files.value).some(file => file.error)

  // ✅ Don't allow form if there's a file error
  if (hasFileErrors) {
    return false
  }

  // Check all required fields are filled
  const allFieldsFilled = requiredFields.every(field => field !== null && field !== '' && field !== undefined)

  // Check all required files are uploaded
  // const allFilesUploaded = Object.values(uploadedFiles.value).every(file => file !== null)

  return allFieldsFilled 
})
const saveOptimiserProgress = async () => {
  try {
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep,
      progress_page: stepsStore.currentStep
    })
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}
const handleContinue = async () => {
  if (isFormComplete.value && !isLoading.value) {
    isLoading.value = true
    const user_id = sessionStorage.getItem('user_id')
    const concpt = localStorage.getItem('concept') || 'hb'; // default to hb for now
    
    // Prepare payload object, map all fields you want to send
    const payload = {
      name: formData.scenarioName,
      season_type: formData.seasonType,
      user_id: user_id,  // or get dynamically
      eval_type: formData.configurationStore,
      event_name: formData.event,
      eval_start: formData.evaluationPeriod.startDate,
      eval_end: formData.evaluationPeriod.endDate,
      ref_start: formData.referencePeriod.startDate,
      ref_end: formData.referencePeriod.endDate,
      loc_cd: JSON.stringify(formData.storeSelection), // or array depending on backend
      CNCPT_NM: "hb",//update cncpt_nm to use concept from localstorage
      TERRITORY_NM: formData.territory?.code || formData.territory, // use code or name accordingly
      metric: formData.performanceMetric,
      sqft_file_id: files.value.sqft.fileId || null,
      sqft_filename: files.value.sqft.filename || null,
      mdq_file_id: files.value.mdq.fileId || null,
      mdq_filename: files.value.mdq.filename || null,
      cover_file_id: files.value.cover.fileId || null,
      cover_filename: files.value.cover.filename || null,
      exclusion_file_id: files.value.exclusion.fileId || null,
      exclusion_filename: files.value.exclusion.filename || null,
      created_by: user_id,
      updated_by: user_id,
    }
    if (route.query.id) {
      payload.id = route.query.id;
    }
    
    try {
      // Call your API method here
      const response = await saveScenarioAPI(payload)
      console.log("Scenario saved:", response)

      if (response && response.scenario_details) {
        scenarioStore.setCurrentScenario(response.scenario_details)
        stepsStore.setScenarioData(response.scenario_details)
      }
      await fetchStoresByTerritory(formData.territory?.code || formData.territory)
      if (formData.configurationStore === 'Selected Stores') {
        sessionStorage.setItem('loc_codes', JSON.stringify(formData.storeSelection || []));
      }
      sessionStorage.setItem('territory_name', formData.territory?.code || formData.territory || '');
      sessionStorage.setItem('concept', 'hb');
      sessionStorage.setItem('performance_metric', formData.performanceMetric || '');
      stepsStore.goToNextStep()
    } catch (error) {
      console.error("Failed to save scenario:", error)
      scenarioStore.setError(error.message || 'Failed to save scenario')
    } finally {
      isLoading.value = false
    }
  }
}
const onConfigurationSelect = async (value) => {  
  if (value === 'Selected Stores') {
    stepsStore.setStepVisible('Set Test & Control', true)
    if (formData.territory) {
      const stores = await fetchStoresByTerritory(formData.territory)
      storeOptions.value = stores
  }
}
  if (value === 'Test & Control') {
    stepsStore.setStepVisible('Set Test & Control', false)
    storeOptions.value = []
    formData.storeSelection = []
  }
  if (value === null) {
    stepsStore.setStepVisible('Set Test & Control', true)
    stepsStore.setStepVisible('View Clusters', true)
    // Clear store options when switching away from Selected Stores
    storeOptions.value = []
    formData.storeSelection = []
  }
}
const handleFileUpload = (key, file) => {
  // Update the file object with the new file
  files.value[key] = {
    ...files.value[key],
    fileObject: file,
    filename: file.name
  }
}
const setStore = (value) => {
  sessionStorage.setItem('store_config',value)
  onConfigurationSelect(value)
}
// Handle upload result from FileUploadArea components
const handleUploadResult = (result) => {  
  if (result.success && result.fileId && result.fileType) {
    // Update the combined file object
    files.value[result.fileType] = {
      ...files.value[result.fileType],
      fileId: result.fileId,
      filename: result.fileName,
      error: false
    }
  } else if (!result.success) {
    if (result.fileType) {
      files.value[result.fileType] = {
        ...files.value[result.fileType],
        fileId: null,
        filename: null,
        error: true
      }
    }
  }
}


const fetchStoresByTerritory = async (territoryCode) => {
  
  loadingStores.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    const response = await axios.post('/scenario/select_store_dropdown/', {
           "concept":"hb",
           "territory":territoryCode
        });
        storeOptions.value = response.data;

        return response.data

  } catch (error) {
    console.error('Error fetching stores:', error)
    return []
  } finally {
    loadingStores.value = false
  }
}

const onTerritorySelect = async (territory) => {
  if (territory && formData.configurationStore === 'Selected Stores') {
    const stores = await fetchStoresByTerritory(territory.code)
    storeOptions.value = stores
    // Clear previous selections when territory changes
    formData.storeSelection = []
  }
}


const onSeasonTypeSelect = (val) => {
  
  if (val === 'in season') {
    // stepsStore.setStepVisible('Range', false)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === 'pre season') {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === null) {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', true)
  }
}

const setDragActive = (key) => {
  dragActive.value = key
}

provide('files', files)
provide('dragActive', dragActive)
provide('handleFileUpload', handleFileUpload)
provide('setDragActive', setDragActive)

function getCurrentMonthYear() {
  const now = new Date();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 01 - 12
  const year = now.getFullYear();
  return `${year}-${month}`;
}

const formData = reactive({
  scenario_id: null,
  configurationStore: null,
  territory: null,
  scenarioName: '',
  seasonPreference: 'in-season',
  seasonType: null,
  performanceMetric: null,
  storeSelection: [],
  event: null,
  evaluationPeriod: {
    startDate: '',
    endDate: ''
  },
  referencePeriod: {
    startDate: '2024-08',
    endDate: getCurrentMonthYear()
  },
  territory: ''
})

// Watch formData deeply and set sessionStorage 'runoptimiser' to true on any change
watch(
  formData,
  () => {
    sessionStorage.setItem('runoptimiser', 'true')
  },
  { deep: true }
)


const territory = [
  { code: 'AE', label: 'UAE' },
  { code: 'BH', label: 'Bahrain' },
  { code: 'EG', label: 'Egypt' },
  { code: 'KS', label: 'KSA' },
  { code: 'KW', label: 'Kuwait' },
  { code: 'OM', label: 'Oman' },
  { code: 'QT', label: 'Qatar' },
  { code: 'LB', label: 'Lebanon' },
  { code: 'JD', label: 'Jordan' }
]



const fillFormFromStore = async (storedData) => {
  if (storedData && Object.keys(storedData).length > 0) {
    formData.scenario_id = storedData.id ?? formData.scenario_id
    formData.configurationStore = storedData.eval_type ?? formData.configurationStore
    formData.territory = storedData.TERRITORY_NM ?? storedData.territory_name ?? formData.territory
    formData.scenarioName = storedData.name ?? formData.scenarioName
    formData.seasonType = storedData.season_type ?? formData.seasonType
    formData.performanceMetric = storedData.metric ?? formData.performanceMetric
    if (formData.seasonType === 'in season') {
      formData.event = storedData.name ?? ''
    } else {
      formData.event = storedData.event_name ?? formData.event
    }
    formData.evaluationPeriod.startDate = formatDate(storedData.eval_start) || formData.evaluationPeriod.startDate
    formData.evaluationPeriod.endDate = formatDate(storedData.eval_end) || formData.evaluationPeriod.endDate
    formData.referencePeriod.startDate = formatMonth(storedData.ref_start) || formData.referencePeriod.startDate
    formData.referencePeriod.endDate = formatMonth(storedData.ref_end) || formData.referencePeriod.endDate
    sessionStorage.setItem('store_config',formData.configurationStore)

    if (formData.configurationStore === 'Selected Stores' && formData.territory) {
      const storeList = await fetchStoresByTerritory(formData.territory?.code || formData.territory)
      storeOptions.value = storeList
    }

    let selected_store = storedData?.loc_cd || storedData?.location_codes || []
    try {
      const parsedSelectedStore = JSON.parse(selected_store)
      formData.storeSelection = parsedSelectedStore
    } catch (e) {
      formData.storeSelection = []
    }
    // Populate the combined files object from store data
    const fileTypes = ['mdq', 'cover', 'exclusion']
    console.log("storedData", storedData)
    fileTypes.forEach(type => {
      const filenameKey = `${type}_filename`
      const fileIdKey = `${type}_file_id`
      
      if (storedData[filenameKey] || storedData[fileIdKey]) {
        files.value[type] = {
          filename: storedData[filenameKey] || null,
          fileId: storedData[fileIdKey] || null,
          fileObject: null // No actual file object when loading from store
        }
      }
    })
  }
}

const validateEvaluationPeriod = () => {
  const { startDate, endDate } = formData.evaluationPeriod
  const today = new Date().toISOString().split('T')[0]


  if (startDate && endDate && endDate < startDate) {
    popupMessage.value = 'Evaluation End Date should not be earlier than Start Date.'
    showPopup.value = true
    isEvaluationPeriodValid.value = false
    return
  }
  isEvaluationPeriodValid.value = true
}

const validateReferencePeriod = () => {
  const { startDate, endDate } = formData.referencePeriod

  if (startDate && endDate && endDate < startDate) {
    popupMessage.value = 'Reference Period End Month should not be earlier than Start Month.'
    showPopup.value = true
    isReferencePeriodValid.value = false
    return
  }
  isReferencePeriodValid.value = true
}


onMounted(() => {
  fillFormFromStore(stepsStore.getScenarioData)
  sessionStorage.setItem('runoptimiser', 'false')
})

const formatRefMonth = (yyyymm) => {
  const str_yyyymm = yyyymm.toString()
  return `${str_yyyymm.slice(0,4)}-${str_yyyymm.slice(4,6)}`
}
// Watch for territory changes and fetch stores
watch(
  () => formData.territory,
  async (newTerritory) => {
    const code = newTerritory?.code || newTerritory || ''
    const concept = localStorage.getItem('concept')
    if (code) {
      await fetchStoresByTerritory(code)
      const response = await fetchRefPeriod(concept, code)
      const newStart = formatRefMonth(response.start_month)
      const newEnd = formatRefMonth(response.end_month)

      endMonth.value = newEnd
      const currentRefStart = formData.referencePeriod.startDate
      const currentRefEnd = formData.referencePeriod.endDate

      // Fix invalid start month
      if (!currentRefStart || currentRefStart < newStart || currentRefStart > newEnd) {
        formData.referencePeriod.startDate = newStart
      }

      // Fix invalid end month
      if (!currentRefEnd || currentRefEnd < newStart || currentRefEnd > newEnd) {
        formData.referencePeriod.endDate = newEnd
      }
    }
  }
)

watch(() => stepsStore.getScenarioData, (newData) => {
  fillFormFromStore(newData)
}, { deep: true, immediate: false })

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date)) return '' // handle invalid date
  return date.toISOString().split('T')[0] // "YYYY-MM-DD"
}
const formatMonth = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date)) return ''
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}` // "YYYY-MM"
}

const storeDropdownOpen = ref(false)
const storeOptions = ref([])
const configurationStoreOptions = ['Test & Control', 'Selected Stores']
const territories = [
  { code: "AE", name: "UAE" },
  { code: "BH", name: "Bahrain" },
  { code: "EG", name: "Egypt" },
  { code: "KS", name: "KSA" },
  { code: "KW", name: "Kuwait" },
  { code: "OM", name: "Oman" },
  { code: "QT", name: "Qatar" },
  { code: "LB", name: "Lebanon" },
  { code: "JD", name: "Jordan" }
]
const seasonTypeOptions = [
  { value: 'in season', label: 'In-Season' },
  { value: 'pre season', label: 'Pre-Season' }
]
const performanceOptions = [
  { value: 'REVENUE', label: 'Revenue' },
  { value: 'GMV', label: 'Margin' }
]

const toggleStoreSelection = (store) => {
  const storeCode = store.code; // extract the code only
  const index = formData.storeSelection.indexOf(storeCode);
  if (index === -1) {
    formData.storeSelection.push(storeCode);  // push only the code
  } else {
    formData.storeSelection.splice(index, 1); // remove by code
  }
};

// const getTerritoryName = (code) => {
//   const found = locations.find(loc => loc.code === code)
//   return found ? found.label : 'Unknown'
// }

const onTerritoryChange = async (selectedTerritory) => {
  console.log('Territory selected:', selectedTerritory)
  const concept = localStorage.getItem('concept')
  // Example: Fetch store list based on selected territory and concept
  if (selectedTerritory) {
    const stores = await fetchStores(concept, selectedTerritory.code)
    console.log("stores value is ", stores)
    storeOptions.value = stores.map(store => ({
      label: store.loc_nm,
      code: store.loc_cd
    }))
  }
}




const generateAnalysis = () => {
  console.log('Generating analysis with:', JSON.stringify(formData, null, 2))
  // You can replace this with actual logic

}


const removeStore = (store) => {
  formData.storeSelection = formData.storeSelection.filter(s => s !== store)
}
const oldScenarioName = ref(formData.scenarioName)
watch(
  () => [formData.seasonType, formData.scenarioName],
  ([newSeasonType, newScenarioName]) => {
    if (newSeasonType === 'in season') {
      // Only auto-set if event field is empty or equal to old scenario name
      if (!formData.event || formData.event === oldScenarioName.value) {
        formData.event = newScenarioName
      }
    }
    else{
      formData.event = ''
    }
    oldScenarioName.value = newScenarioName
  },
  { immediate: true }
)

</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
::placeholder {
  color:  #d1d5db;     /* grey text */
  font-size: 14px;
}
</style>