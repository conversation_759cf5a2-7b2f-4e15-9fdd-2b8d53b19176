<template>
    <div class="">
        <!-- Step Navigation -->
        <div class="p-6 bg-secondary ">
            <div class="flex w-full gap-2 bg-[#F9FAFB] p-2 rounded-lg overflow-x-auto">
                <!-- <div class="px-3 py-1 rounded cursor-pointer flex items-center gap-1" @click="goToHome">
                    <Home class="w-4 h-4" />
                </div> -->
                <template v-for="(step, index) in stepsStore.visibleSteps" :key="index">
                    <div class="px-2 py-1 rounded cursor-pointer flex items-center font-semibold text-sm
"
                        :class="{
                            'bg-[#16A34A] text-white': stepsStore.currentStep === index,
                            'bg-[#F3F4F6] text-gray-300 hover:cursor-no-drop': stepsStore.currentStep !== index && step.disabled,
                            
                        }"
                        @click="stepsStore.goToStep(index)">
                        {{ step.name }}
                    </div>
                    <span v-if="index < stepsStore.visibleSteps.length - 1" class="text-gray-300 text-sm flex items-center">></span>
                </template>
            </div>
        </div>

        <!-- Step Content -->
         <!-- <div class="w-full overflow-x-auto bg-[#F9FAFB] shadow rounded-lg"> -->
    <div class=" bg-[#F9FAFB] shadow rounded-lg overflow-auto">
      <component v-if="stepComponentReady" :is="stepsStore.activeStep.component"/>
    </div>

        <!-- Navigation -->
        <div v-if="!noNextButtons.includes(stepsStore.activeStep.name)" class="flex justify-between mt-4 ml-8">
          <button @click="saveOptimiserProgress('prev')"
            class="border border-gray-300 text-black px-4 font-semibold py-2  mb-8 rounded hover:bg-gray-100 ">
            ← Previous
          </button>
          <button v-if="!noNextButtons.includes(stepsStore.activeStep.name)" @click="saveOptimiserProgress('next')"
            class="bg-green-600 hover:bg-green-700 text-white font-semibold mr-8 mb-8 cursor-pointer hover:bg-green-900 px-6 py-2 rounded mr-2"
            :disabled="stepsStore.currentStep === stepsStore.steps.length - 1">
            Next →
          </button>
        </div>
    </div>
</template>


<script setup>
import { onMounted, ref, onUnmounted, watch } from 'vue'
import { Home } from 'lucide-vue-next'
import { useRoute, useRouter } from 'vue-router'
import BusinessDashboard from './BusinessDashboard.vue'
import StoreCluster from './StoreCluster.vue'
import ControlStoreSelection from './ControlStoreSelection.vue'
import SpaceDataSummary from './SpaceDataSummary.vue'
import SpaceHealthDashboard from './SpaceHealthDashboard.vue'
import EvaluationDashboard from './EvaluationDashboard.vue'
import OptimizationSummary from './Optimization.vue'
import RangeBasedOptimisationSummary from './OptimizationSummary.vue'
import SaturationPoint from './ProductivityChartsDashboard.vue'
import OutlierHandle from './OutlierHandle.vue'
import PerformanceCalculation from './PerformanceCalculation.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import {updateScenarioStatus} from '../services/api.js'
import axios from 'axios'

// --- Watch sessionStorage store_config and call onConfigurationSelect ---
import { watchEffect } from 'vue'
const storeConfigRef = ref(sessionStorage.getItem('store_config'))
let lastStoreConfig = storeConfigRef.value
setInterval(() => {
  const current = sessionStorage.getItem('store_config')
  if (current !== lastStoreConfig) {
    lastStoreConfig = current
    storeConfigRef.value = current
  }
}, 300)

watch(storeConfigRef, (val, oldVal) => {
  if (val && val !== oldVal) {
    onConfigurationSelect(val)
  }
})

const stepsStore = useStepsStore()
const currentStep = ref(0)
const router = useRouter()
const route = useRoute()
const stepComponentReady = ref(false)

onUnmounted(() => {
  stepsStore.clearScenarioData()
  sessionStorage.clear()
})
const noNextButtons = ['Saturation Analysis', 'Set Test & Control', 'Enter Details','Optimize']
const goToHome = () => {
    router.push({ name: '/spaceoptimization/HomePage' })
}
const scenario_id = null
onMounted(async () => {
  sessionStorage.setItem('runoptimiser', 'false')
  const status = route.query.status
  const id = route.query.id
  const finishSetup = () => { stepComponentReady.value = true }
  if (id) {
    // If id is present in query, use it (do not create new)
    if (status === 'update') {
      await getOptimizer(id)
      await onConfigurationSelect(sessionStorage.getItem('store_config'))
      finishSetup()
    } else if (status === 'create') {
      stepsStore.steps.forEach((step, index) => {
        step.disabled = index !== 0        
        step.hidden = false
      })  
      stepsStore.currentStep = 0
      stepsStore.clearScenarioData()
      await getOptimizer(id)
      await onConfigurationSelect(sessionStorage.getItem('store_config'))
      finishSetup()
    } else {
      finishSetup()
    }
    return
  } else if (status === 'create') {
    // No id, so create new
    stepsStore.steps.forEach((step, index) => {
      step.disabled = index !== 0        
      step.hidden = false
    })
    stepsStore.currentStep = 0
    stepsStore.clearScenarioData()
    await createNewOptimiser()
    finishSetup()
    return
  } else if (status === 'update') {
    await getOptimizer(id)
    finishSetup()
  } else {
    router.push({ name: 'HomePage' })
  }
})
const onConfigurationSelect = async (value) => {  
  if (value === 'Selected Stores') {
    stepsStore.setStepVisible('View Clusters', false)
    stepsStore.setStepVisible('Set Test & Control', true)
  }
  if (value === 'Set Test & Control') {
    stepsStore.setStepVisible('Set Test & Control', false)
  }
  if (value === null) {
    stepsStore.setStepVisible('Set Test & Control', true)
    stepsStore.setStepVisible('View Clusters', true)
  }
}
const getOptimizer = async (id) => {
  try {
    const response = await axios.post('/scenario/getOptimizerDetails/', {
      "scenario_id": id
    })
    const data = response.data.data
    stepsStore.setScenarioData(data)
    // Set sessionStorage as before
    sessionStorage.setItem('scenario_id', data.id)
    sessionStorage.setItem('concept', data.CNCPT_NM)
    sessionStorage.setItem('territory_name', data.TERRITORY_NM)
    sessionStorage.setItem('performance_metric', data.metric)
    sessionStorage.setItem('weights', JSON.stringify(data.weights))
    sessionStorage.setItem('store_config', data.eval_type)
    if (data.eval_type === 'Test & Control') {
      sessionStorage.setItem('testControlMappings', data.loc_cd)
      const parsedData = JSON.parse(data.loc_cd)
      const locCodes = parsedData.flatMap(item => [
        item.testStore,
        ...(item.controlStores || [])
      ])
      sessionStorage.setItem('loc_codes', JSON.stringify(locCodes))
      stepsStore.setStepVisible('Set Test & Control', true)
    } else {
      sessionStorage.setItem('loc_codes', data.loc_cd)
      stepsStore.setStepVisible('Set Test & Control', false)
    }

    // Enable all steps up to progress_page-1, disable after
    stepsStore.steps.forEach((step, idx) => {
      step.disabled = idx > (data.progress_page)
      step.hidden = false
    })
    // Set current step to current_page-1 (0-based index)
    stepsStore.currentStep = Math.max(0, (data.current_page || 1))
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}
const createNewOptimiser = async () => {
  try {
    const response = await axios.post('/scenario/createOptimizer/')
    console.log(response, 'response')
    if (response && response.data.id) {
      sessionStorage.setItem('scenario_id', response.data.id)
      router.replace({
        query: {
          ...route.query,
          id: response.data.id,
          status: 'create'

        }
      })
    }
  } catch (err) {
    console.error('Error creating new optimizer:', err)
  }
}
const saveOptimiserProgress = async (action) => {
  try {
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep ,
      progress_page: stepsStore.currentStep 
    })
    if (action === 'next') {
      stepsStore.goToNextStep()
    } else if (action === 'prev') {
      stepsStore.goToPreviousStep()
    }
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}

// Track last step to determine direction
let lastStep = stepsStore.currentStep
watch(
  () => stepsStore.currentStep,
  async (newStep, oldStep) => {
    if (newStep !== oldStep) {
      // Determine direction
      // const action = newStep > oldStep ? 'next' : 'prev'
      await saveOptimiserProgress()
    }
    lastStep = newStep
  }
)
</script>

<style scoped>
/* Optional scroll behavior */
::-webkit-scrollbar {
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
}
</style>