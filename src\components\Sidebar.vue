<script setup lang="ts">
import { ref, inject, computed } from 'vue';
import {
  ChevronLeft,
  ChevronRight,
  FileText,
  BarChart2,
  ChartScatter,
  Settings,
  LogOut,
  Home,
  FileBox,
  Gauge,
  Boxes,
  Users,
  SquareMousePointer,
  Target,
  Outlier // Use an appropriate icon
} from 'lucide-vue-next';

const baseImgUrl = inject('baseImageUrl');

const props = defineProps({
  isExpanded: Boolean
});

const notificationCount = ref(3);
const username = computed(() => JSON.parse(localStorage.getItem('user') || ''));

const userInitial = computed(() => {
  if (!username.value) return '';
  // Take part before "@", split by "."
  const parts = username.value.split('@')[0].split('.');
  return parts.map(p => p.charAt(0).toUpperCase()).join('');
});

const userFullname = computed(() => {
  if (!username.value) return '';
  // Take part before "@", split by "."
  const parts = username.value.split('@')[0].split('.');
  return parts
    .map(p => p.charAt(0).toUpperCase() + p.slice(1).toLowerCase())
    .join(' ');
});
// const userInitial = computed(() => username.value.charAt(0).toUpperCase());

// Get user role from localStorage (or your auth system)
const userRole = computed(() => localStorage.getItem('role') || '');

// Menu items, User Dashboard only for superadmin
const menuItems = computed(() => [
  {
    label: 'Home',
    path: '/HomePage',
    icon: Home
  },
  ...(userRole.value === 'superadmin'
    ? [{
        label: 'User Dashboard',
        path: '/user-dashboard',
        icon: Users
      }]
    : [])
]);
</script>

<template>
  <div class="h-full flex flex-col transition-all duration-300 bg-[#16A34A] text-white shadow-lg">
    <!-- Top Section: Logo -->
    <div class="flex items-center p-4 border-b border-primary flex-shrink-0" :class="{ 'justify-center': !isExpanded }">
      <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
        <img  :src="`${baseImgUrl}/space_logo.png`" class="w-6 h-6" alt="Smart Space Logo" />
      </div>
      <span v-if="isExpanded" class="ml-3 text-primary text-lg font-semibold whitespace-nowrap">Smart Space</span>
    </div>

    <!-- Main Navigation Items - Scrollable if needed -->
    <nav class="flex-1 mt-6 space-y-2 px-3 overflow-y-auto">
      <router-link
        v-for="item in menuItems"
        :key="item.path"
        :to="item.path"
        class="flex items-center py-2 px-3 rounded-lg text-primary hover:bg-primary hover:text-secondary transition-colors"
        :class="[
          { 'justify-center': !isExpanded },
          $route.path === item.path ? 'bg-primary text-secondary font-bold shadow' : ''
        ]"
      >
        <component :is="item.icon" class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          {{ item.label }}
        </span>
      </router-link>
    </nav>

    <!-- Bottom Section: Settings, Logout, Profile - Fixed at bottom -->
    <div class="flex-shrink-0 px-3 pb-3 space-y-2">
      <!-- Settings -->
      <!-- <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-primary hover:bg-primary hover:text-secondary transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <Settings class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Settings
        </span>
      </router-link> -->

      <!-- Log out -->
      <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-primary hover:bg-primary hover:text-secondary transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <LogOut class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Log out
        </span>
      </router-link>

      <!-- User Profile -->
      <div
        class="flex items-center py-2 px-3 bg-secondary text-primary rounded-lg"
        :class="{ 'justify-center': !isExpanded }"
      >
        <div class="w-8 h-8 flex items-center justify-center bg-primary text-tertiary rounded-full text-sm font-bold">
          {{ userInitial }}
        </div>
        <span v-if="isExpanded" class="ml-3 text-sm text-primary font-medium whitespace-nowrap">
          {{ userFullname }}
        </span>
      </div>
    </div>
  </div>
</template>