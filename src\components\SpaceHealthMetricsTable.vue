<template>
  <div class="space-y-4">
    <div>
      <!-- <h2 class="text-2xl font-bold text-gray-700 mb-4">Hierarchical Table</h2> -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="overflow-x-auto">
          <table
            class="w-full text-sm border-collapse border border-gray-200 rounded-lg"
          >
            <thead class="bg-gray-100 text-sm sticky top-0 font-bold">
              <tr>
                <th
                  rowspan="2"
                  class="py-3 px-4 text-left text-xs font-bold text-gray-700 border-r border-gray-300 bg-gray-100 min-w-[300px] sticky left-0"
                >
                  Hierarchy
                </th>
                <th
                  colspan="7"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 border-r border-gray-300 bg-blue-100 border-b"
                >
                  Area Metrics
                </th>
                <th
                  colspan="4"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 border-r border-gray-300 bg-green-100 border-b"
                >
                  Stock Metrics
                </th>
                <th
                  colspan="4"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 border-r border-gray-300 border-b"
                >
                  Sales Metrics(AED)
                </th>
                <th
                  colspan="12"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 border-r border-gray-300 bg-orange-100 border-b"
                >
                  Area Productivity
                </th>
                <th
                  colspan="3"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 border-r border-gray-300 bg-red-100 border-b"
                >
                  Inventory Health
                </th>
              </tr>
              <tr>
                <th
                  v-for="header in areaMetricsHeaders"
                  :key="header"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px] top-[35px] bg-blue-100"
                >
                  {{ header }}
                </th>
                <th
                  v-for="header in stockMetricsHeaders"
                  :key="header"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px] top-[35px] bg-green-100"
                >
                  {{ header }}
                </th>
                <th
                  v-for="header in salesMetricsHeaders"
                  :key="header"
                  class="py-2 px-2 text-center text-xs font-bold text-gray-700 whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px] top-[35px]"
                >
                  {{ header }}
                </th>
                <th
                  v-for="header in areaProductivityHeaders"
                  :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-orange-100 text-gray-700 whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px] top-[35px]"
                >
                  {{ header }}
                </th>
                <th
                  v-for="header in inventoryHealthHeaders"
                  :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-red-100 text-gray-700 whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px] top-[35px]"
                >
                  {{ header }}
                </th>
              </tr>
            </thead>

            <!-- Hierarchical Body -->
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="node in displayedHierarchicalRows"
                :key="node.uniqueKey"
                :class="[
                  'hover:bg-gray-100 transition-colors border-b border-gray-300',
                  {
                    'font-bold bg-gray-50': node.level === 0,
                    'font-semibold': node.level === 1 || node.level === 2 || node.level === 3,
                    'bg-gray-50/50': node.level === 2,
                  },
                ]"
              >
                <!-- Hierarchy Column -->
                <td
                  class="py-2 px-3 text-left bg-white border-r border-gray-300 sticky left-0 min-w-[300px]"
                >
                  <div
                    class="flex items-center"
                    :style="{ paddingLeft: `${node.level * 24}px` }"
                  >
                    <button
                      @click="toggleNode(node)"
                      v-if="node.children && node.children.length > 0"
                      class="mr-2 w-5 text-center text-gray-600 hover:text-black focus:outline-none transition-colors"
                    >
                      {{ node.expanded ? "−" : "+" }}
                    </button>
                    <div v-else class="w-7"></div>
                    <span>{{ getHierarchyLabel(node) }}</span>
                  </div>
                </td>

                <!-- Area Metrics -->
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.lm, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.sqft, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "lmRank") }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "sqftRank") }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.fixtureDensity, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.lmCont,0) }}%
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.sqftCont, 0) }}%
                </td>

                <!-- Stock Metrics -->
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.optionCount) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.optionDensity, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.sohQty) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.stockDensity, 2) }}
                </td>

                <!-- Sales Metrics -->
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revenue) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmv) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revenuePerDay) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmvPerDay) }}
                </td>

                <!-- Area Productivity -->
                  <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revPerLM) }}
                </td>
                  <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmvPerLM) }}
                </td>
                  <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revPerSqft) }}
                </td>
                  <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmvPerSqft) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revPerLmPerDay, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.revPerSqftPerDay, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmvPerLmPerDay, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatCurrency(node.data?.gmvPerSqftPerDay, 2) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "revPerLmPerDayRank") }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "revPerSqftPerDayRank") }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "gmvPerLmPerDayRank") }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ getFieldValue(node.data, "gmvPerSqftPerDayRank") }}
                </td>

                <!-- Inventory Health -->
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.invCount, 0) }}
                </td>
                <td class="py-2 px-3 text-center border-r border-gray-200">
                  {{ formatNumber(node.data?.ros) }}
                </td>
                <td class="py-2 px-3 text-center">
                  {{ formatNumber(getFieldValue(node.data, "cover")) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive } from "vue";

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: null,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 50,
  },
  totalRows: {
    type: Number,
    default: 0,
  },
  totalPages: {
    type: Number,
    default: 1,
  },
  nextPageNum: {
    type: Number,
    default: null,
  },
  previousPageNum: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(["page-changed"]);

const areaMetricsHeaders = [
  "LM",
  "Sqft",
  "LM Rank",
  "Sqft Rank",
  "Fixture Density",
  "LM Cont",
  "Sqft Cont",
];

const stockMetricsHeaders = [
  "Option Count",
  "Option Density",
  "SOH Qty",
  "Stock Density",
];

const salesMetricsHeaders = [
  "Revenue",
  "GMV",
  "Revenue per day",
  "GMV per day",
];

const areaProductivityHeaders = [
  "Rev per LM",
  "GMV per LM",
  "Rev per Sqft",
  "GMV per Sqft",
  "Rev per LM per day",
  "Rev per Sqft per day",
  "GMV per LM per day",
  "GMV per Sqft per day",
  "Rev per LM per day (Rank)",
  "Rev per Sqft per day (Rank)",
  "GMV per LM per day (Rank)",
  "GMV per Sqft per day (Rank)",
];

const inventoryHealthHeaders = ["Current Stock", "Average Weekly Sales", "Cover (days)"];

const hierarchyState = reactive({});

const getFieldValue = (row, field) => {
  if (!row) return "-";
  const value = row[field];
  return value == null || value === "" ? "-" : value;
};

const formatNumber = (value, decimals = 0) => {
  if (value == null || value === "" || isNaN(value)) return "-";
  return Number(value).toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

const formatCurrency = (value,decimals = 0) => {
  if (value == null || value === "") return "-";
  return new Intl.NumberFormat("en-AE", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

const getHierarchyLabel = (node) => {
  if (!node.data) return "-";
  console.log("node value is ", node)
  switch (node.level) {
    case 0:
      return `Store: ${node.data.locNm || node.data.locCode || "-"}`;
    case 1:
      return `Group: ${node.data.group || "-"}`;
    case 2:
      return `Department: ${node.data.department || "-"}`;
    case 3:
      return `Class: ${node.data.class || "-"}`;
    case 4:
      return `Subclass: ${node.data.subClass || "-"}`;
    default:
      return "-";
  }
};

/**
 * Aggregates all numeric fields from children into totals.
 */
const aggregateMetrics = (children) => {
  if (!children || children.length === 0) return {};
  console.log("aggregate ")
  const totals = {};
  children.forEach((child) => {
    const d = child.data || {};
    for (const key in d) {
      if (key === "cover") continue;
      if (typeof d[key] === "number") {
        totals[key] = (totals[key] || 0) + d[key];
      }
    }
  });

  totals.cover = "-";

  return totals;
};


// Build hierarchical data structure

const hierarchicalData = computed(() => {
  if (!props.data || props.data.length === 0) return [];

  const stores = {};

  // First pass: Build the structure with leaf data
  props.data.forEach((row) => {
    const { locCode, locNm, group, department, class: className, subClass } = row;

    if (!locCode) return;

    // Initialize store
    if (!stores[locCode]) {
      stores[locCode] = {
    data: { locCode, locNm },
        children: {},
        level: 0,
        uniqueKey: locCode,
      };
    }

    // Only process rows that have actual data (leaf nodes or specific level data)
    if (group) {
      // Initialize group
      if (!stores[locCode].children[group]) {
        stores[locCode].children[group] = {
          data: { locCode, group },
          children: {},
          level: 1,
          uniqueKey: `${locCode} > ${group}`,
        };
      }

      if (department) {
        // Initialize department
        if (!stores[locCode].children[group].children[department]) {
          stores[locCode].children[group].children[department] = {
            data: { locCode, group, department },
            children: {},
            level: 2,
            uniqueKey: `${locCode} > ${group} > ${department}`,
          };
        }

        if (className) {
          // Initialize class
          if (
            !stores[locCode].children[group].children[department].children[
              className
            ]
          ) {
            stores[locCode].children[group].children[department].children[
              className
            ] = {
              data: { locCode, group, department, class: className },
              children: {},
              level: 3,
              uniqueKey: `${locCode} > ${group} > ${department} > ${className}`,
            };
          }

          if (subClass) {
            // Add subclass (leaf node with actual data)
            stores[locCode].children[group].children[department].children[
              className
            ].children[subClass] = {
              data: row,
              children: null,
              level: 4,
              uniqueKey: `${locCode} > ${group} > ${department} > ${className} > ${subClass}`,
            };
          }
        }
      }
    }
  });

const aggregateNode = (node) => {
  if (!node.children || Object.keys(node.children).length === 0) {
    return {
      ...node,
      children: null,
      expanded: hierarchyState[node.uniqueKey] || false,
    };
  }

  const processedChildren = Object.values(node.children).map(aggregateNode);

  const aggregatedData = aggregateMetrics(processedChildren);

  const totalLM   = aggregatedData.lm || 0;
  const totalSqft = aggregatedData.sqft || 0;
  const totalRev  = aggregatedData.revenue || 0;
  const totalGMV  = aggregatedData.gmv || 0;

  const optionCount = aggregatedData.optionCount || 0;
  const sohQty      = aggregatedData.sohQty || 0;

  aggregatedData.fixtureDensity =
    totalSqft > 0 ? totalLM / totalSqft : 0;

  aggregatedData.optionDensity =
    totalLM > 0 ? optionCount / totalLM : 0;

  aggregatedData.stockDensity =
    totalLM > 0 ? sohQty / totalLM : 0;

  aggregatedData.revPerLM    = totalLM   > 0 ? totalRev / totalLM   : 0;
  aggregatedData.gmvPerLM    = totalLM   > 0 ? totalGMV / totalLM   : 0;
  aggregatedData.revPerSqft  = totalSqft > 0 ? totalRev / totalSqft : 0;
  aggregatedData.gmvPerSqft = totalSqft > 0 ? totalGMV / totalSqft : 0;
  
  aggregatedData.revPerLmPerDay    = totalLM   > 0 ? aggregatedData.revenuePerDay / totalLM   : 0;
  aggregatedData.gmvPerLmPerDay    = totalLM   > 0 ? aggregatedData.gmvPerDay / totalLM   : 0;
  aggregatedData.revPerSqftPerDay  = totalSqft > 0 ? aggregatedData.revenuePerDay / totalSqft : 0;
  aggregatedData.gmvPerSqftPerDay  = totalSqft > 0 ? aggregatedData.gmvPerDay / totalSqft : 0;

  return {
    ...node,
    data: {
      ...node.data,
      ...aggregatedData,
    },
    children: processedChildren,
    expanded: hierarchyState[node.uniqueKey] || false,
  };
};


  const result = Object.values(stores).map(aggregateNode);
  applyGlobalRanking(result);



  return result;
});

// Flatten nested structure for rendering based on expanded state
const displayedHierarchicalRows = computed(() => {
  const rows = [];
  const addNodeToRows = (node) => {
    rows.push(node);
    if (node.expanded && node.children) {
      node.children.forEach(addNodeToRows);
    }
  };
  hierarchicalData.value.forEach(addNodeToRows);
  return rows;
});

const toggleNode = (node) => {
  const newState = !(hierarchyState[node.uniqueKey] || false);

  // find siblings (all nodes at same level under same parent)
  let siblings = [];
  if (node.level === 0) {
    // top-level (stores)
    siblings = hierarchicalData.value;
  } else {
    const parentKey = node.uniqueKey.split(" > ").slice(0, -1).join(" > ");
    const findParent = (nodes) => {
      for (const n of nodes) {
        if (n.uniqueKey === parentKey) return n;
        if (n.children) {
          const res = findParent(n.children);
          if (res) return res;
        }
      }
      return null;
    };
    const parent = findParent(hierarchicalData.value);
    siblings = parent?.children || [];
  }

  // apply new state to all siblings
  siblings.forEach((s) => {
    hierarchyState[s.uniqueKey] = newState;
    s.expanded = newState;

    // collapse all children when expanding siblings
    if (s.children) {
      const collapseChildren = (children) => {
        children.forEach((child) => {
          hierarchyState[child.uniqueKey] = false;
          child.expanded = false;
          if (child.children) collapseChildren(child.children);
        });
      };
      collapseChildren(s.children);
    }
  });
};



const collectNodesByLevel = (nodes, level, result = []) => {
  nodes.forEach(node => {
    if (node.level === level) result.push(node);
    if (node.children) collectNodesByLevel(node.children, level, result);
  });
  return result;
};

// Generic ranking function
const rankNodes = (nodes, metricField, rankField) => {
  const sorted = [...nodes].sort((a, b) => (b.data?.[metricField] || 0) - (a.data?.[metricField] || 0));
  sorted.forEach((node, index) => {
    node.data[rankField] = index + 1; // global rank for that level
  });
};

const applyGlobalRanking = (hierarchy) => {
  // For each level (0 = store, 1 = group, 2 = dept, 3 = class, 4 = subclass)
  for (let level = 0; level <= 4; level++) {
    const nodesAtLevel = collectNodesByLevel(hierarchy, level);
    if (nodesAtLevel.length > 0) {
      rankNodes(nodesAtLevel, "lm", "lmRank");
      rankNodes(nodesAtLevel, "sqft", "sqftRank");
      rankNodes(nodesAtLevel, "revPerLmPerDay", "revPerLmPerDayRank");
      rankNodes(nodesAtLevel, "revPerSqftPerDay", "revPerSqftPerDayRank");
      rankNodes(nodesAtLevel, "gmvPerLmPerDay", "gmvPerLmPerDayRank");
      rankNodes(nodesAtLevel, "gmvPerSqftPerDay", "gmvPerSqftPerDayRank");
    }
  }
};

</script>
